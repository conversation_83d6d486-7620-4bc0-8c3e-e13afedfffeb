import { useState } from "react";
import NavigationHeader from "./NavigationHeader";
import TodoSection from "./TodoSection";
import AdminMessages from "./AdminMessages";
import QuickStats from "./QuickStats";
import TeamMembers from "./TeamMembers";
import UpcomingTasks from "./UpcomingTasks";
import AdminPanel from "./AdminPanel";
import FloatingActionButton from "./FloatingActionButton";
import AddTaskModal from "./AddTaskModal";
import ConnectionStatus from "./ConnectionStatus";

export default function MainApp() {
  const [showAdmin, setShowAdmin] = useState(false);
  const [showAddTask, setShowAddTask] = useState(false);

  return (
    <div className="min-h-screen">
      <NavigationHeader onShowAdmin={() => setShowAdmin(true)} />
      <ConnectionStatus />

      <div className="max-w-7xl mx-auto p-4 lg:p-8">
        <div className="grid grid-cols-1 lg:grid-cols-3 gap-6">
          <div className="lg:col-span-2 space-y-6">
            <TodoSection />
            <AdminMessages />
          </div>

          <div className="space-y-6">
            <QuickStats />
            <TeamMembers />
            <UpcomingTasks />
          </div>
        </div>
      </div>

      {/* Floating Action Button */}
      <FloatingActionButton
        onAddTask={() => setShowAddTask(true)}
        onShowProfile={() => {
          // This will be handled by NavigationHeader's keyboard shortcuts
          document.dispatchEvent(new KeyboardEvent('keydown', {
            key: 'p',
            ctrlKey: true
          }));
        }}
        onShowSearch={() => {
          // This will be handled by NavigationHeader's keyboard shortcuts
          document.dispatchEvent(new KeyboardEvent('keydown', {
            key: 'k',
            ctrlKey: true
          }));
        }}
      />

      {/* Modals */}
      {showAdmin && <AdminPanel onClose={() => setShowAdmin(false)} />}
      {showAddTask && <AddTaskModal onClose={() => setShowAddTask(false)} />}
    </div>
  );
}
