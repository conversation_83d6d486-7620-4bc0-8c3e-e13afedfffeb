import { useState, useEffect } from "react";
import { User } from "firebase/auth";
import { auth, handleRedirect, db, withRetry, handleFirestoreError } from "@/lib/firebase";
import { onAuthStateChanged } from "firebase/auth";
import { doc, setDoc, serverTimestamp } from "firebase/firestore";
import { handlePotentialOfflineError } from "@/lib/offlineMode";

export function useAuth() {
  const [user, setUser] = useState<User | null>(null);
  const [loading, setLoading] = useState(false); // Set to false to skip loading

  useEffect(() => {
    console.log('useAuth: Starting authentication check...');

    const unsubscribe = onAuthStateChanged(auth, async (user) => {
      console.log('useAuth: Auth state changed, user:', user ? `logged in as ${user.email}` : 'not logged in');
      console.log('useAuth: Full user object:', user);

      setUser(user); // Set user immediately

      if (user) {
        // Create or update user document in Firestore with retry logic
        try {
          await withRetry(async () => {
            await setDoc(doc(db, "users", user.uid), {
              uid: user.uid,
              displayName: user.displayName || user.email?.split('@')[0] || "User",
              email: user.email || "",
              photoURL: user.photoURL || null,
              isOnline: true,
              lastSeen: serverTimestamp(),
              role: "Member", // Default role
              tasksCompleted: 0,
              createdAt: serverTimestamp(),
              updatedAt: serverTimestamp()
            }, { merge: true }); // Use merge to not overwrite existing data
          });
        } catch (error: any) {
          // Check if this is an offline error
          if (handlePotentialOfflineError(error)) {
            console.log("Switched to offline mode due to Firestore error");
          } else {
            const errorMessage = handleFirestoreError(error);
            console.error("Error creating/updating user document:", errorMessage);
          }
          // Don't block authentication for Firestore errors
        }
      }

      console.log('useAuth: User state updated to:', user ? user.email : 'null');
    });

    // Handle redirect result when component mounts
    handleRedirect().catch(console.error);

    return () => {
      unsubscribe();
    };
  }, []);

  return { user, loading };
}
