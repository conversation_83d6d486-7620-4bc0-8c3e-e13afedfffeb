import { useMessages } from "@/hooks/useFirestore";

export default function MessagesBroadcast() {
  const { messages, loading } = useMessages();

  const formatTimeAgo = (timestamp: any) => {
    if (!timestamp) return "";
    const date = timestamp.toDate ? timestamp.toDate() : new Date(timestamp);
    const now = new Date();
    const diffInMinutes = Math.floor((now.getTime() - date.getTime()) / (1000 * 60));
    
    if (diffInMinutes < 1) return "Just now";
    if (diffInMinutes < 60) return `${diffInMinutes} min ago`;
    if (diffInMinutes < 1440) return `${Math.floor(diffInMinutes / 60)} hour${Math.floor(diffInMinutes / 60) > 1 ? 's' : ''} ago`;
    return `${Math.floor(diffInMinutes / 1440)} day${Math.floor(diffInMinutes / 1440) > 1 ? 's' : ''} ago`;
  };

  if (loading) {
    return (
      <div className="glass rounded-3xl p-6">
        <h3 className="text-xl font-bold text-white mb-4">Team Messages</h3>
        <div className="animate-pulse space-y-3">
          {[1, 2, 3].map((i) => (
            <div key={i} className="h-16 bg-white/10 rounded-2xl"></div>
          ))}
        </div>
      </div>
    );
  }

  return (
    <div className="glass rounded-3xl p-6">
      <h3 className="text-xl font-bold text-white mb-4">Team Messages</h3>
      
      <div className="space-y-3">
        {messages.length === 0 ? (
          <div className="text-center py-8">
            <i className="fas fa-comments text-4xl text-white/20 mb-4"></i>
            <p className="text-white/60">No messages yet</p>
            <p className="text-white/40 text-sm">Admin messages will appear here</p>
          </div>
        ) : (
          messages.slice(0, 5).map((message) => (
            <div key={message.id} className="glass-dark rounded-2xl p-4">
              <div className="flex items-start space-x-3">
                <div className="w-8 h-8 bg-gradient-to-br from-red-400 to-red-600 rounded-full flex items-center justify-center flex-shrink-0">
                  <i className="fas fa-crown text-white text-xs"></i>
                </div>
                <div className="flex-1">
                  <div className="flex items-center space-x-2 mb-1">
                    <span className="text-white font-medium text-sm">Admin</span>
                    <span className="text-white/50 text-xs">
                      {formatTimeAgo(message.createdAt)}
                    </span>
                    {message.isUrgent && (
                      <span className="bg-red-500/20 text-red-300 px-2 py-1 rounded text-xs">
                        Urgent
                      </span>
                    )}
                  </div>
                  {message.title && (
                    <h4 className="text-white font-medium text-sm mb-1">{message.title}</h4>
                  )}
                  <p className="text-white/80 text-sm">{message.content}</p>
                </div>
              </div>
            </div>
          ))
        )}
      </div>
    </div>
  );
}
