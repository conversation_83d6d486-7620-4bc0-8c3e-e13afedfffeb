@import url('https://fonts.googleapis.com/css2?family=Inter:wght@300;400;500;600;700&display=swap');

@tailwind base;
@tailwind components;
@tailwind utilities;

:root {
  --background: 210 11% 98%;
  --foreground: 240 10% 4%;
  --muted: 210 11% 96%;
  --muted-foreground: 215 16% 47%;
  --popover: 0 0% 100%;
  --popover-foreground: 240 10% 4%;
  --card: 0 0% 100%;
  --card-foreground: 240 10% 4%;
  --border: 215 28% 92%;
  --input: 215 28% 92%;
  --primary: 247 84% 67%;
  --primary-foreground: 211 100% 99%;
  --secondary: 285 84% 67%;
  --secondary-foreground: 24 9% 10%;
  --accent: 210 11% 96%;
  --accent-foreground: 24 9% 10%;
  --destructive: 0 84% 60%;
  --destructive-foreground: 60 9% 98%;
  --ring: 247 84% 67%;
  --radius: 0.75rem;

  /* Glass morphism colors */
  --glass-bg: rgba(255, 255, 255, 0.1);
  --glass-border: rgba(255, 255, 255, 0.2);
  --glass-dark-bg: rgba(0, 0, 0, 0.2);
  --glass-dark-border: rgba(255, 255, 255, 0.1);
}

.dark {
  --background: 240 10% 4%;
  --foreground: 0 0% 98%;
  --muted: 240 4% 16%;
  --muted-foreground: 240 5% 65%;
  --popover: 240 10% 4%;
  --popover-foreground: 0 0% 98%;
  --card: 240 10% 4%;
  --card-foreground: 0 0% 98%;
  --border: 240 4% 16%;
  --input: 240 4% 16%;
  --primary: 247 84% 67%;
  --primary-foreground: 211 100% 99%;
  --secondary: 240 4% 16%;
  --secondary-foreground: 0 0% 98%;
  --accent: 240 4% 16%;
  --accent-foreground: 0 0% 98%;
  --destructive: 0 63% 31%;
  --destructive-foreground: 0 0% 98%;
  --ring: 247 84% 67%;
}

@layer base {
  * {
    @apply border-border;
  }

  body {
    @apply font-sans antialiased text-foreground;
    font-family: 'Inter', sans-serif;
    background: #000000;
    background-image:
      radial-gradient(circle at 20% 80%, rgba(120, 119, 198, 0.3) 0%, transparent 50%),
      radial-gradient(circle at 80% 20%, rgba(255, 119, 198, 0.3) 0%, transparent 50%),
      radial-gradient(circle at 40% 40%, rgba(120, 119, 198, 0.2) 0%, transparent 50%);
    min-height: 100vh;
  }
}

@layer utilities {
  .glass {
    background: var(--glass-bg);
    backdrop-filter: blur(10px);
    border: 1px solid var(--glass-border);
    border-radius: 8px;
  }

  .glass-dark {
    background: var(--glass-dark-bg);
    backdrop-filter: blur(10px);
    border: 1px solid var(--glass-dark-border);
    border-radius: 8px;
  }

  .blocky {
    border-radius: 8px !important;
    box-shadow: 0 4px 8px rgba(0, 0, 0, 0.3);
  }

  .blocky-card {
    border-radius: 12px !important;
    border: 2px solid rgba(255, 255, 255, 0.1);
    box-shadow: 0 8px 16px rgba(0, 0, 0, 0.4);
  }

  .gradient-bg {
    background: linear-gradient(135deg, hsl(var(--primary)) 0%, hsl(var(--secondary)) 100%);
  }

  .gradient-bg-alt {
    background: linear-gradient(135deg, hsl(var(--primary)) 0%, hsl(var(--secondary)) 50%, #f093fb 100%);
  }

  .smooth-transition {
    transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
  }

  .hover-glass:hover {
    background: rgba(255, 255, 255, 0.15);
    transform: translateY(-2px);
  }

  /* Touch and Animation Enhancements */
  .touch-feedback {
    transition: all 0.2s cubic-bezier(0.4, 0, 0.2, 1);
    transform-origin: center;
  }

  .touch-feedback:active {
    transform: scale(0.95);
    background: rgba(255, 255, 255, 0.2);
  }

  .bounce-in {
    animation: bounceIn 0.6s cubic-bezier(0.68, -0.55, 0.265, 1.55);
  }

  .fade-in {
    animation: fadeIn 0.4s ease-out;
  }

  .slide-up {
    animation: slideUp 0.5s cubic-bezier(0.4, 0, 0.2, 1);
  }

  .pulse-glow {
    animation: pulseGlow 2s infinite;
  }

  /* Close Button Enhancements */
  .close-btn {
    position: relative;
    display: flex;
    align-items: center;
    justify-content: center;
    width: 2.5rem;
    height: 2.5rem;
    border-radius: 9999px;
    background: linear-gradient(135deg, #ef4444, #dc2626);
    border: 2px solid #ef4444;
    color: #ffffff;
    transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
    box-shadow: 0 4px 15px rgba(239, 68, 68, 0.4), 0 0 0 0 rgba(239, 68, 68, 0.4);
    font-weight: bold;
    z-index: 1000;
  }

  .close-btn:hover {
    transform: scale(1.15);
    background: linear-gradient(135deg, #dc2626, #b91c1c);
    box-shadow: 0 8px 25px rgba(239, 68, 68, 0.6), 0 0 30px rgba(239, 68, 68, 0.4);
    border-color: #fca5a5;
  }

  .close-btn:active {
    transform: scale(0.9);
    box-shadow: 0 2px 10px rgba(239, 68, 68, 0.8);
  }

  /* Enhanced Close Button for Mobile */
  @media (max-width: 768px) {
    .close-btn {
      width: 3rem;
      height: 3rem;
      font-size: 1.25rem;
      border-width: 3px;
      box-shadow: 0 6px 20px rgba(239, 68, 68, 0.5), 0 0 0 0 rgba(239, 68, 68, 0.4);
    }

    .close-btn:hover {
      transform: scale(1.1);
      box-shadow: 0 10px 30px rgba(239, 68, 68, 0.7), 0 0 40px rgba(239, 68, 68, 0.5);
    }
  }

  /* Responsive Modal Enhancements */
  .modal-responsive {
    width: 100%;
    max-width: 24rem;
    margin-left: 1rem;
    margin-right: 1rem;
  }

  @media (min-width: 640px) {
    .modal-responsive {
      max-width: 42rem;
      margin-left: 1.5rem;
      margin-right: 1.5rem;
    }
  }

  @media (min-width: 768px) {
    .modal-responsive {
      max-width: 56rem;
      margin-left: 2rem;
      margin-right: 2rem;
    }
  }

  @media (min-width: 1024px) {
    .modal-responsive {
      max-width: 72rem;
      margin-left: auto;
      margin-right: auto;
    }
  }

  /* Mobile Touch Optimizations */
  @media (max-width: 768px) {
    .touch-target {
      min-height: 44px;
      min-width: 44px;
    }

    .modal-content {
      padding: 1rem;
      max-height: 95vh;
    }

    .close-btn {
      width: 3rem;
      height: 3rem;
      font-size: 1.125rem;
    }
  }
}

/* Keyframe Animations */
@keyframes bounceIn {
  0% {
    opacity: 0;
    transform: scale(0.3);
  }
  50% {
    opacity: 1;
    transform: scale(1.05);
  }
  70% {
    transform: scale(0.9);
  }
  100% {
    opacity: 1;
    transform: scale(1);
  }
}

@keyframes fadeIn {
  from {
    opacity: 0;
  }
  to {
    opacity: 1;
  }
}

@keyframes slideUp {
  from {
    opacity: 0;
    transform: translateY(30px);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}

@keyframes pulseGlow {
  0%, 100% {
    box-shadow: 0 0 5px rgba(120, 119, 198, 0.5);
  }
  50% {
    box-shadow: 0 0 20px rgba(120, 119, 198, 0.8), 0 0 30px rgba(255, 119, 198, 0.6);
  }
}

/* Enhanced Navigation Bar */
nav.glass-dark {
  background: rgba(0, 0, 0, 0.9) !important;
  backdrop-filter: blur(30px) !important;
  border-bottom: 2px solid rgba(147, 51, 234, 0.4) !important;
  box-shadow: 0 4px 20px rgba(147, 51, 234, 0.15) !important;
}

/* Floating Action Button Animations */
.fab-bounce {
  animation: fabBounce 0.3s cubic-bezier(0.68, -0.55, 0.265, 1.55);
}

@keyframes fabBounce {
  0% { transform: scale(0.8); }
  50% { transform: scale(1.1); }
  100% { transform: scale(1); }
}

/* Enhanced Scrollbar */
::-webkit-scrollbar {
  width: 8px;
}

::-webkit-scrollbar-track {
  background: rgba(0, 0, 0, 0.2);
  border-radius: 4px;
}

::-webkit-scrollbar-thumb {
  background: rgba(147, 51, 234, 0.5);
  border-radius: 4px;
}

::-webkit-scrollbar-thumb:hover {
  background: rgba(147, 51, 234, 0.7);
}

/* Enhanced Glass Effects */
.glass-enhanced {
  background: rgba(0, 0, 0, 0.6);
  backdrop-filter: blur(25px);
  border: 1px solid rgba(255, 255, 255, 0.15);
  box-shadow: 0 8px 32px rgba(0, 0, 0, 0.3);
}

/* Notification Badge Animation */
@keyframes notificationPulse {
  0%, 100% {
    transform: scale(1);
    opacity: 1;
  }
  50% {
    transform: scale(1.1);
    opacity: 0.8;
  }
}

.notification-badge {
  animation: notificationPulse 2s infinite;
}
