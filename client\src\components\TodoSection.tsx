import { useState } from "react";
import { useRealtimeTasks } from "@/hooks/useRealtimeDatabase";
import { Button } from "@/components/ui/button";
import { Checkbox } from "@/components/ui/checkbox";
import { useToast } from "@/hooks/use-toast";
import AddTaskModal from "./AddTaskModal";

export default function TodoSection() {
  const { tasks, loading, updateTask, deleteTask } = useRealtimeTasks();
  const [showAddTask, setShowAddTask] = useState(false);
  const [filter, setFilter] = useState("all");
  const { toast } = useToast();

  const handleToggleComplete = async (id: string, currentStatus: string) => {
    try {
      const newStatus = currentStatus === "completed" ? "active" : "completed";
      await updateTask(id, { status: newStatus });
      toast({
        title: "Task Updated",
        description: `Task marked as ${newStatus}`,
      });
    } catch (error) {
      toast({
        title: "Error",
        description: "Failed to update task",
        variant: "destructive",
      });
    }
  };

  const handleDeleteTask = async (id: string) => {
    try {
      await deleteTask(id);
      toast({
        title: "Task Deleted",
        description: "Task has been successfully deleted",
      });
    } catch (error) {
      toast({
        title: "Error",
        description: "Failed to delete task",
        variant: "destructive",
      });
    }
  };

  const filteredTasks = tasks.filter((task) => {
    switch (filter) {
      case "active":
        return task.status === "active";
      case "completed":
        return task.status === "completed";
      case "high":
        return task.priority === "high";
      default:
        return true;
    }
  });

  const getPriorityColor = (priority: string) => {
    switch (priority) {
      case "high":
        return "bg-red-500/20 text-red-300";
      case "medium":
        return "bg-blue-500/20 text-blue-300";
      case "low":
        return "bg-green-500/20 text-green-300";
      default:
        return "bg-gray-500/20 text-gray-300";
    }
  };

  const formatDate = (timestamp: any) => {
    if (!timestamp) return "";
    const date = timestamp.toDate ? timestamp.toDate() : new Date(timestamp);
    return date.toLocaleDateString();
  };

  if (loading) {
    return (
      <div className="lg:col-span-2">
        <div className="glass rounded-3xl p-6 mb-6">
          <div className="animate-pulse space-y-4">
            <div className="h-8 bg-white/10 rounded w-1/4"></div>
            <div className="space-y-3">
              {[1, 2, 3].map((i) => (
                <div key={i} className="h-20 bg-white/10 rounded-2xl"></div>
              ))}
            </div>
          </div>
        </div>
      </div>
    );
  }

  return (
    <>
      <div className="lg:col-span-2">
        <div className="glass rounded-2xl sm:rounded-3xl p-4 sm:p-6 mb-4 sm:mb-6">
          {/* Header - responsive layout */}
          <div className="flex flex-col sm:flex-row sm:items-center justify-between mb-4 sm:mb-6 gap-3 sm:gap-0">
            <h2 className="text-xl sm:text-2xl font-bold text-white">My Tasks</h2>
            <Button
              onClick={() => setShowAddTask(true)}
              className="bg-white/20 hover:bg-white/30 text-white touch-feedback pulse-glow w-full sm:w-auto"
            >
              <i className="fas fa-plus mr-2"></i>
              <span className="sm:hidden">Add New Task</span>
              <span className="hidden sm:inline">Add Task</span>
            </Button>
          </div>

          {/* Filter buttons - responsive scrollable layout */}
          <div className="flex space-x-2 mb-4 sm:mb-6 overflow-x-auto pb-2 scrollbar-hide">
            <Button
              onClick={() => setFilter("all")}
              className={`whitespace-nowrap touch-feedback min-w-fit px-3 sm:px-4 py-2 text-sm sm:text-base ${
                filter === "all"
                  ? "bg-white/30 text-white slide-up"
                  : "glass hover:bg-white/15 text-white/80"
              }`}
              variant="ghost"
            >
              All
            </Button>
            <Button
              onClick={() => setFilter("active")}
              className={`whitespace-nowrap touch-feedback min-w-fit px-3 sm:px-4 py-2 text-sm sm:text-base ${
                filter === "active"
                  ? "bg-white/30 text-white slide-up"
                  : "glass hover:bg-white/15 text-white/80"
              }`}
              variant="ghost"
            >
              Active
            </Button>
            <Button
              onClick={() => setFilter("completed")}
              className={`whitespace-nowrap touch-feedback min-w-fit px-3 sm:px-4 py-2 text-sm sm:text-base ${
                filter === "completed"
                  ? "bg-white/30 text-white slide-up"
                  : "glass hover:bg-white/15 text-white/80"
              }`}
              variant="ghost"
            >
              Completed
            </Button>
            <Button
              onClick={() => setFilter("high")}
              className={`whitespace-nowrap touch-feedback min-w-fit px-3 sm:px-4 py-2 text-sm sm:text-base ${
                filter === "high"
                  ? "bg-white/30 text-white slide-up"
                  : "glass hover:bg-white/15 text-white/80"
              }`}
              variant="ghost"
            >
              <span className="sm:hidden">High</span>
              <span className="hidden sm:inline">High Priority</span>
            </Button>
          </div>

          <div className="space-y-3">
            {filteredTasks.length === 0 ? (
              <div className="text-center py-8">
                <i className="fas fa-tasks text-4xl text-white/20 mb-4"></i>
                <p className="text-white/60">No tasks found</p>
                <p className="text-white/40 text-sm">Create your first task to get started</p>
              </div>
            ) : (
              filteredTasks.map((task) => (
                <div
                  key={task.id}
                  className="glass hover-glass rounded-xl sm:rounded-2xl p-3 sm:p-4 smooth-transition slide-up"
                >
                  {/* Mobile-first responsive task layout */}
                  <div className="flex items-start space-x-3">
                    <div className="flex-shrink-0 mt-1">
                      <Checkbox
                        checked={task.status === "completed"}
                        onCheckedChange={() => handleToggleComplete(task.id, task.status)}
                        className="border-white/30 data-[state=checked]:bg-[hsl(var(--primary))] touch-target"
                      />
                    </div>

                    <div className="flex-1 min-w-0">
                      <h3
                        className={`font-medium text-sm sm:text-base break-words ${
                          task.status === "completed"
                            ? "text-white/70 line-through"
                            : "text-white"
                        }`}
                      >
                        {task.title}
                      </h3>

                      {task.description && (
                        <p className="text-white/60 text-xs sm:text-sm mt-1 break-words">
                          {task.description}
                        </p>
                      )}

                      {/* Responsive tags and dates */}
                      <div className="flex flex-wrap items-center gap-2 mt-2">
                        <span
                          className={`px-2 py-1 rounded-lg text-xs ${getPriorityColor(
                            task.priority
                          )}`}
                        >
                          <span className="sm:hidden">{task.priority.charAt(0).toUpperCase()}</span>
                          <span className="hidden sm:inline">
                            {task.priority.charAt(0).toUpperCase() + task.priority.slice(1)} Priority
                          </span>
                        </span>

                        {task.dueDate && (
                          <span className="text-white/50 text-xs">
                            <span className="sm:hidden">Due: {formatDate(task.dueDate)}</span>
                            <span className="hidden sm:inline">Due: {formatDate(task.dueDate)}</span>
                          </span>
                        )}

                        {task.status === "completed" && (
                          <span className="text-white/50 text-xs">
                            <span className="sm:hidden">✓ {formatDate(task.updatedAt)}</span>
                            <span className="hidden sm:inline">Completed: {formatDate(task.updatedAt)}</span>
                          </span>
                        )}
                      </div>
                    </div>

                    {/* Action buttons - responsive */}
                    <div className="flex flex-col sm:flex-row space-y-1 sm:space-y-0 sm:space-x-1 flex-shrink-0">
                      <Button
                        variant="ghost"
                        size="sm"
                        className="text-white/60 hover:text-white p-2 touch-target min-w-[2.5rem] h-10"
                      >
                        <i className="fas fa-edit text-xs sm:text-sm"></i>
                      </Button>
                      <Button
                        onClick={() => handleDeleteTask(task.id)}
                        variant="ghost"
                        size="sm"
                        className="text-red-400 hover:text-red-300 p-2 touch-target min-w-[2.5rem] h-10"
                      >
                        <i className="fas fa-trash text-xs sm:text-sm"></i>
                      </Button>
                    </div>
                  </div>
                </div>
              ))
            )}
          </div>
        </div>
      </div>

      {showAddTask && <AddTaskModal onClose={() => setShowAddTask(false)} />}
    </>
  );
}
