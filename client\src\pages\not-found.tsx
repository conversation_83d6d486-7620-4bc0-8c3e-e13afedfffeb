import { Card, CardContent } from "@/components/ui/card";
import { AlertCircle, Home, ArrowLeft } from "lucide-react";
import { Button } from "@/components/ui/button";
import { useLocation } from "wouter";

export default function NotFound() {
  const [, setLocation] = useLocation();

  return (
    <div className="min-h-screen w-full flex items-center justify-center p-4 sm:p-6 lg:p-8">
      {/* Responsive container with proper spacing */}
      <div className="w-full max-w-sm sm:max-w-md lg:max-w-lg xl:max-w-xl">
        <Card className="glass-dark border-white/20 shadow-2xl">
          <CardContent className="p-6 sm:p-8 lg:p-10">
            {/* Icon and title section - responsive sizing */}
            <div className="text-center mb-6 sm:mb-8">
              <div className="w-16 h-16 sm:w-20 sm:h-20 lg:w-24 lg:h-24 glass rounded-full flex items-center justify-center mx-auto mb-4 sm:mb-6 pulse-glow">
                <AlertCircle className="h-8 w-8 sm:h-10 sm:w-10 lg:h-12 lg:w-12 text-red-400" />
              </div>

              {/* Responsive typography */}
              <h1 className="text-2xl sm:text-3xl lg:text-4xl font-bold text-white mb-2 sm:mb-3">
                404 - Page Not Found
              </h1>

              <p className="text-sm sm:text-base lg:text-lg text-white/70 leading-relaxed">
                The page you're looking for doesn't exist or has been moved.
              </p>
            </div>

            {/* Action buttons - responsive layout */}
            <div className="flex flex-col sm:flex-row gap-3 sm:gap-4">
              <Button
                onClick={() => setLocation("/")}
                className="flex-1 bg-gradient-to-r from-[hsl(var(--primary))] to-[hsl(var(--secondary))] hover:opacity-80 text-white font-semibold py-2 sm:py-3 touch-feedback"
              >
                <Home className="h-4 w-4 sm:h-5 sm:w-5 mr-2" />
                <span className="text-sm sm:text-base">Go Home</span>
              </Button>

              <Button
                onClick={() => window.history.back()}
                variant="outline"
                className="flex-1 glass border-white/30 text-white hover:bg-white/10 py-2 sm:py-3 touch-feedback"
              >
                <ArrowLeft className="h-4 w-4 sm:h-5 sm:w-5 mr-2" />
                <span className="text-sm sm:text-base">Go Back</span>
              </Button>
            </div>

            {/* Additional help text - responsive */}
            <div className="mt-6 sm:mt-8 p-3 sm:p-4 glass-dark rounded-xl">
              <p className="text-xs sm:text-sm text-white/60 text-center">
                If you believe this is an error, please contact support or try refreshing the page.
              </p>
            </div>
          </CardContent>
        </Card>
      </div>
    </div>
  );
}
