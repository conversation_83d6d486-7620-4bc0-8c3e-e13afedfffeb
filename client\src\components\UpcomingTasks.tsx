import { useRealtimeTasks } from "@/hooks/useRealtimeDatabase";

export default function UpcomingTasks() {
  const { tasks, loading } = useRealtimeTasks();

  // Filter and sort upcoming tasks
  const upcomingTasks = tasks
    .filter(task => {
      if (task.status === "completed") return false;
      if (!task.dueDate) return false;
      
      const dueDate = task.dueDate.toDate ? task.dueDate.toDate() : new Date(task.dueDate);
      const now = new Date();
      const diffDays = Math.ceil((dueDate.getTime() - now.getTime()) / (1000 * 60 * 60 * 24));
      
      return diffDays >= 0 && diffDays <= 7; // Next 7 days
    })
    .sort((a, b) => {
      const dateA = a.dueDate.toDate ? a.dueDate.toDate() : new Date(a.dueDate);
      const dateB = b.dueDate.toDate ? b.dueDate.toDate() : new Date(b.dueDate);
      return dateA.getTime() - dateB.getTime();
    })
    .slice(0, 5); // Show only 5 upcoming tasks

  const formatDueDate = (dueDate: any) => {
    const date = dueDate.toDate ? dueDate.toDate() : new Date(dueDate);
    const now = new Date();
    const diffMs = date.getTime() - now.getTime();
    const diffDays = Math.ceil(diffMs / (1000 * 60 * 60 * 24));
    
    if (diffDays === 0) return "Today";
    if (diffDays === 1) return "Tomorrow";
    if (diffDays < 7) return `${diffDays} days`;
    return date.toLocaleDateString();
  };

  const getPriorityColor = (priority: string) => {
    switch (priority) {
      case "high":
        return "text-red-400";
      case "medium":
        return "text-yellow-400";
      case "low":
        return "text-green-400";
      default:
        return "text-gray-400";
    }
  };

  const getUrgencyColor = (dueDate: any) => {
    const date = dueDate.toDate ? dueDate.toDate() : new Date(dueDate);
    const now = new Date();
    const diffDays = Math.ceil((date.getTime() - now.getTime()) / (1000 * 60 * 60 * 24));
    
    if (diffDays === 0) return "bg-red-500/20 border-red-500/50";
    if (diffDays === 1) return "bg-orange-500/20 border-orange-500/50";
    if (diffDays <= 3) return "bg-yellow-500/20 border-yellow-500/50";
    return "bg-blue-500/20 border-blue-500/50";
  };

  if (loading) {
    return (
      <div className="glass rounded-3xl p-6 slide-up">
        <h3 className="text-xl font-bold text-white mb-4 flex items-center">
          <i className="fas fa-calendar-alt mr-2 text-purple-400"></i>
          Upcoming Tasks
        </h3>
        <div className="animate-pulse space-y-3">
          {[1, 2, 3].map((i) => (
            <div key={i} className="glass-dark rounded-xl p-3">
              <div className="h-4 bg-white/10 rounded w-3/4 mb-2"></div>
              <div className="h-3 bg-white/10 rounded w-1/2"></div>
            </div>
          ))}
        </div>
      </div>
    );
  }

  return (
    <div className="glass rounded-3xl p-6 slide-up">
      <div className="flex items-center justify-between mb-6">
        <h3 className="text-xl font-bold text-white flex items-center">
          <i className="fas fa-calendar-alt mr-2 text-purple-400 pulse-glow"></i>
          Upcoming Tasks
        </h3>
        <div className="flex items-center space-x-2">
          <div className="w-2 h-2 bg-purple-400 rounded-full animate-pulse"></div>
          <span className="text-white/60 text-sm">Next 7 days</span>
        </div>
      </div>

      <div className="space-y-3">
        {upcomingTasks.length === 0 ? (
          <div className="text-center py-8">
            <i className="fas fa-calendar-check text-4xl text-white/20 mb-4"></i>
            <p className="text-white/60">No upcoming tasks</p>
            <p className="text-white/40 text-sm">You're all caught up for the week!</p>
          </div>
        ) : (
          upcomingTasks.map((task) => (
            <div
              key={task.id}
              className={`glass-dark rounded-xl p-4 border transition-all duration-200 hover:bg-white/10 touch-feedback ${getUrgencyColor(task.dueDate)}`}
            >
              <div className="flex items-start space-x-3">
                {/* Priority Indicator */}
                <div className={`w-3 h-3 rounded-full mt-2 ${
                  task.priority === 'high' ? 'bg-red-400' :
                  task.priority === 'medium' ? 'bg-yellow-400' :
                  'bg-green-400'
                }`}></div>

                {/* Task Content */}
                <div className="flex-1 min-w-0">
                  <h4 className="text-white font-medium truncate mb-1">
                    {task.title}
                  </h4>
                  {task.description && (
                    <p className="text-white/60 text-sm truncate mb-2">
                      {task.description}
                    </p>
                  )}
                  
                  <div className="flex items-center space-x-3">
                    <span className={`text-xs px-2 py-1 rounded-full bg-gray-500/20 ${getPriorityColor(task.priority)}`}>
                      {task.priority.charAt(0).toUpperCase() + task.priority.slice(1)}
                    </span>
                    <span className="text-white/50 text-xs flex items-center">
                      <i className="fas fa-clock mr-1"></i>
                      Due {formatDueDate(task.dueDate)}
                    </span>
                  </div>
                </div>

                {/* Quick Actions */}
                <div className="flex items-center space-x-1">
                  <button className="w-8 h-8 glass rounded-lg flex items-center justify-center hover:bg-white/10 touch-feedback">
                    <i className="fas fa-check text-green-400 text-sm"></i>
                  </button>
                  <button className="w-8 h-8 glass rounded-lg flex items-center justify-center hover:bg-white/10 touch-feedback">
                    <i className="fas fa-edit text-blue-400 text-sm"></i>
                  </button>
                </div>
              </div>
            </div>
          ))
        )}
      </div>

      {/* Quick Stats */}
      <div className="mt-6 pt-4 border-t border-white/10">
        <div className="grid grid-cols-3 gap-3">
          <div className="text-center">
            <div className="text-lg font-bold text-red-400">
              {tasks.filter(t => {
                if (!t.dueDate || t.status === "completed") return false;
                const date = t.dueDate.toDate ? t.dueDate.toDate() : new Date(t.dueDate);
                return Math.ceil((date.getTime() - new Date().getTime()) / (1000 * 60 * 60 * 24)) === 0;
              }).length}
            </div>
            <div className="text-xs text-white/60">Due Today</div>
          </div>
          <div className="text-center">
            <div className="text-lg font-bold text-yellow-400">
              {tasks.filter(t => {
                if (!t.dueDate || t.status === "completed") return false;
                const date = t.dueDate.toDate ? t.dueDate.toDate() : new Date(t.dueDate);
                const diffDays = Math.ceil((date.getTime() - new Date().getTime()) / (1000 * 60 * 60 * 24));
                return diffDays >= 1 && diffDays <= 3;
              }).length}
            </div>
            <div className="text-xs text-white/60">This Week</div>
          </div>
          <div className="text-center">
            <div className="text-lg font-bold text-blue-400">
              {tasks.filter(t => {
                if (!t.dueDate || t.status === "completed") return false;
                const date = t.dueDate.toDate ? t.dueDate.toDate() : new Date(t.dueDate);
                return date.getTime() < new Date().getTime();
              }).length}
            </div>
            <div className="text-xs text-white/60">Overdue</div>
          </div>
        </div>
      </div>

      {/* View All Button */}
      <div className="mt-4">
        <button className="w-full glass rounded-lg p-3 hover:bg-white/10 touch-feedback transition-all duration-200 text-center">
          <i className="fas fa-calendar-week text-purple-400 mr-2"></i>
          <span className="text-white text-sm">View Full Calendar</span>
        </button>
      </div>
    </div>
  );
}
