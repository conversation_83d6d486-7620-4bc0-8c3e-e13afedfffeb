import { useState, useEffect } from "react";
import {
  collection,
  query,
  where,
  orderBy,
  onSnapshot,
  addDoc,
  updateDoc,
  deleteDoc,
  doc,
  serverTimestamp,
  getDocs,
} from "firebase/firestore";
import { db } from "@/lib/firebase";
import { useAuth } from "./useAuth";

export function useTasks() {
  const { user } = useAuth();
  const [tasks, setTasks] = useState<any[]>([]);
  const [loading, setLoading] = useState(true);

  useEffect(() => {
    if (!user) {
      setTasks([]);
      setLoading(false);
      return;
    }

    const q = query(
      collection(db, "tasks"),
      where("userId", "==", user.uid),
      orderBy("createdAt", "desc")
    );

    const unsubscribe = onSnapshot(q, (snapshot) => {
      const tasksData = snapshot.docs.map(doc => ({
        id: doc.id,
        ...doc.data()
      }));
      setTasks(tasksData);
      setLoading(false);
    });

    return unsubscribe;
  }, [user]);

  const addTask = async (taskData: any) => {
    if (!user) return;
    
    await addDoc(collection(db, "tasks"), {
      ...taskData,
      userId: user.uid,
      status: "active",
      createdAt: serverTimestamp(),
      updatedAt: serverTimestamp(),
    });
  };

  const updateTask = async (id: string, updates: any) => {
    await updateDoc(doc(db, "tasks", id), {
      ...updates,
      updatedAt: serverTimestamp(),
    });
  };

  const deleteTask = async (id: string) => {
    await deleteDoc(doc(db, "tasks", id));
  };

  return { tasks, loading, addTask, updateTask, deleteTask };
}

export function useMessages() {
  const [messages, setMessages] = useState<any[]>([]);
  const [loading, setLoading] = useState(true);

  useEffect(() => {
    const q = query(
      collection(db, "messages"),
      orderBy("createdAt", "desc")
    );

    const unsubscribe = onSnapshot(q, (snapshot) => {
      const messagesData = snapshot.docs.map(doc => ({
        id: doc.id,
        ...doc.data()
      }));
      setMessages(messagesData);
      setLoading(false);
    });

    return unsubscribe;
  }, []);

  const addMessage = async (messageData: any) => {
    await addDoc(collection(db, "messages"), {
      ...messageData,
      createdAt: serverTimestamp(),
    });
  };

  return { messages, loading, addMessage };
}

// Hook for admin task management
export function useAdminTasks() {
  const { user } = useAuth();
  const [allTasks, setAllTasks] = useState<any[]>([]);
  const [allUsers, setAllUsers] = useState<any[]>([]);
  const [loading, setLoading] = useState(true);

  useEffect(() => {
    if (!user) {
      setAllTasks([]);
      setAllUsers([]);
      setLoading(false);
      return;
    }

    // Listen to all tasks for admin
    const tasksQuery = query(
      collection(db, "tasks"),
      orderBy("createdAt", "desc")
    );

    const unsubscribeTasks = onSnapshot(tasksQuery, (snapshot) => {
      const tasksData = snapshot.docs.map(doc => ({
        id: doc.id,
        ...doc.data()
      }));
      setAllTasks(tasksData);
      setLoading(false);
    });

    // Listen to all users for admin
    const usersQuery = query(
      collection(db, "users"),
      orderBy("createdAt", "desc")
    );

    const unsubscribeUsers = onSnapshot(usersQuery, (snapshot) => {
      const usersData = snapshot.docs.map(doc => ({
        id: doc.id,
        ...doc.data()
      }));
      setAllUsers(usersData);
    });

    return () => {
      unsubscribeTasks();
      unsubscribeUsers();
    };
  }, [user]);

  const adminDeleteTask = async (taskId: string, taskDetails: any) => {
    if (!user) return;
    
    // Log admin action
    await addDoc(collection(db, "adminActions"), {
      adminId: user.uid,
      action: "delete_task",
      targetId: taskId,
      details: JSON.stringify(taskDetails),
      createdAt: serverTimestamp(),
    });

    // Delete the task
    await deleteDoc(doc(db, "tasks", taskId));
  };

  const adminUpdateTask = async (taskId: string, updates: any, originalTask: any) => {
    if (!user) return;

    // Log admin action
    await addDoc(collection(db, "adminActions"), {
      adminId: user.uid,
      action: "modify_task",
      targetId: taskId,
      details: JSON.stringify({ original: originalTask, updates }),
      createdAt: serverTimestamp(),
    });

    // Update the task
    await updateDoc(doc(db, "tasks", taskId), {
      ...updates,
      updatedAt: serverTimestamp(),
      lastModifiedBy: user.uid,
    });
  };

  const getUserById = (userId: string) => {
    return allUsers.find(u => u.userId === userId || u.id === userId);
  };

  return { 
    allTasks, 
    allUsers, 
    loading, 
    adminDeleteTask, 
    adminUpdateTask, 
    getUserById 
  };
}
