# Admin Messaging System Guide

## Overview
The TaskMaster Pro application includes a comprehensive admin messaging system that allows administrators to send messages to team members. The system is integrated with Firebase for real-time messaging and includes both online and offline functionality.

## Admin Credentials
- **Email**: <EMAIL>
- **Password**: tamilselvan

## Team Members
The system is configured to display the following team members:
1. **Pugazh** - Lead Developer (<EMAIL>)
2. **Arun** - Developer (<EMAIL>)  
3. **Tamilselvan** - Developer (<EMAIL>)

## How to Access Admin Messaging

### 1. Login as Admin
1. Open the TaskMaster Pro application
2. Click "Sign In" if not already logged in
3. Enter the admin credentials:
   - Email: `<EMAIL>`
   - Password: `tamilselvan`
4. Click "Sign In"

### 2. Access Admin Messages
Once logged in as admin, you will see:
- An "Admin Messages" section on the main dashboard
- An orange "Admin" badge next to your name in the messages section
- A message composition form (only visible to admins)

### 3. Send Messages
As an admin, you can:
1. **Type your message** in the text input field
2. **Select message type**:
   - 📢 Announcement
   - ⚠️ Alert  
   - ℹ️ Info
3. **Set priority level**:
   - 🟢 Low Priority
   - 🟡 Medium Priority
   - 🔴 High Priority
4. **Choose recipients**:
   - Select specific team members using checkboxes
   - Use "Select All" to send to everyone
   - Use "Clear" to deselect all
   - If no one is selected, message goes to all members
5. **Click "Send Message"** to broadcast

## Features

### Real-time Messaging
- Messages appear instantly for all users
- Live connection status indicator
- Automatic retry on connection issues

### Message Types & Priorities
- **Announcements**: General team updates
- **Alerts**: Important notifications requiring attention
- **Info**: Informational messages
- Priority levels help organize message importance

### Team Member Management
- Shows online/offline status of team members
- Displays member count in real-time
- Selective message targeting

### Offline Support
- Works in offline mode with cached data
- Shows offline indicator when disconnected
- Prevents sending when offline (with clear indication)

## Admin Panel Access
Admins also have access to additional features:
1. **Admin Task Manager**: Manage all user tasks
2. **Team Member Overview**: View all team members
3. **System Controls**: Additional administrative functions

## Technical Details

### Authentication
- Admin status is determined by email address (`<EMAIL>`)
- Firebase Authentication handles login/logout
- Admin privileges are checked on both client and server side

### Data Storage
- Messages stored in Firebase Firestore (`adminMessages` collection)
- Team member data in Firestore (`users` collection)
- Real-time updates via Firestore listeners

### Security
- Only authenticated admins can send messages
- All users can view messages
- Firebase security rules enforce permissions

## Troubleshooting

### Can't Send Messages
- Ensure you're logged in with admin credentials
- Check internet connection (offline mode prevents sending)
- Verify Firebase connection status

### Messages Not Appearing
- Check connection status indicator
- Refresh the page if needed
- Verify Firebase configuration

### Team Members Not Showing
- System falls back to mock data if Firebase is unavailable
- Check console for any connection errors
- Ensure proper Firebase setup

## Support
For technical issues or questions about the admin messaging system, check the browser console for detailed error messages and connection status.
