import { useState, useEffect } from "react";
import { useAuth } from "@/hooks/useAuth";
import { collection, onSnapshot, query, orderBy, addDoc, serverTimestamp, limit } from "firebase/firestore";
import { db, withRetry, handleFirestoreError } from "@/lib/firebase";
import { Button } from "@/components/ui/button";
import { Input } from "@/components/ui/input";
import { useToast } from "@/hooks/use-toast";
import {
  handlePotentialOfflineError,
  getOfflineMode,
  simulateNetworkDelay
} from "@/lib/offlineMode";

interface AdminMessage {
  id: string;
  message: string;
  sender: {
    uid: string;
    displayName: string;
    email: string;
  };
  timestamp: any;
  recipients: string[];
  priority: "low" | "medium" | "high";
  type: "announcement" | "alert" | "info";
}

interface TeamMember {
  id: string;
  displayName: string;
  email: string;
  isOnline: boolean;
}

export default function AdminMessages() {
  const { user } = useAuth();
  const { toast } = useToast();
  const [messages, setMessages] = useState<AdminMessage[]>([]);
  const [members, setMembers] = useState<TeamMember[]>([]);
  const [newMessage, setNewMessage] = useState("");
  const [selectedMembers, setSelectedMembers] = useState<string[]>([]);
  const [priority, setPriority] = useState<"low" | "medium" | "high">("medium");
  const [messageType, setMessageType] = useState<"announcement" | "alert" | "info">("announcement");
  const [loading, setLoading] = useState(false); // Start with false to skip loading
  const [sending, setSending] = useState(false);

  // Load messages with enhanced Firebase integration
  useEffect(() => {
    console.log('AdminMessages: Starting to load messages');

    const loadMessages = async () => {
      try {
        // Check offline mode
        const offlineMode = getOfflineMode();
        if (offlineMode.isOffline) {
          console.log('AdminMessages: Using offline mode');
          setMessages([]);
          return;
        }

        // Add network delay simulation for better UX
        await simulateNetworkDelay(200);

        const messagesQuery = query(
          collection(db, "adminMessages"),
          orderBy("timestamp", "desc"),
          limit(20) // Limit to recent 20 messages for performance
        );

        const unsubscribe = onSnapshot(
          messagesQuery,
          (snapshot) => {
            console.log('AdminMessages: Received data from Firestore');
            const messagesList: AdminMessage[] = [];
            snapshot.forEach((doc) => {
              const data = doc.data();
              messagesList.push({
                id: doc.id,
                ...data,
                // Ensure timestamp is properly handled
                timestamp: data.timestamp || serverTimestamp()
              } as AdminMessage);
            });
            setMessages(messagesList);
          },
          (error) => {
            console.error('AdminMessages: Firestore error:', error);

            // Handle potential offline error
            if (handlePotentialOfflineError(error)) {
              console.log('AdminMessages: Switched to offline mode');
              setMessages([]);
            } else {
              const errorMessage = handleFirestoreError(error);
              console.error('AdminMessages: Error:', errorMessage);
              setMessages([]);
            }
          }
        );

        return unsubscribe;
      } catch (error) {
        console.error('AdminMessages: Setup error:', error);
        setMessages([]);
      }
    };

    loadMessages();
  }, []);

  // Load team members with enhanced Firebase integration
  useEffect(() => {
    console.log('AdminMessages: Starting to load team members');

    const loadMembers = async () => {
      try {
        // Check offline mode
        const offlineMode = getOfflineMode();
        if (offlineMode.isOffline) {
          console.log('AdminMessages: Using offline mode for members');
          // Use mock members for offline mode
          const mockMembers: TeamMember[] = [
            {
              id: "1",
              displayName: "Pugazh",
              email: "<EMAIL>",
              isOnline: true
            },
            {
              id: "2",
              displayName: "Arun",
              email: "<EMAIL>",
              isOnline: true
            },
            {
              id: "3",
              displayName: "Tamilselvan",
              email: "<EMAIL>",
              isOnline: true
            }
          ];
          setMembers(mockMembers);
          return;
        }

        // Add network delay simulation
        await simulateNetworkDelay(200);

        const membersQuery = query(
          collection(db, "users"),
          orderBy("displayName")
        );

        const unsubscribe = onSnapshot(
          membersQuery,
          (snapshot) => {
            console.log('AdminMessages: Received members data from Firestore');
            const membersList: TeamMember[] = [];
            snapshot.forEach((doc) => {
              const data = doc.data();
              membersList.push({
                id: doc.id,
                displayName: data.displayName || data.email?.split('@')[0] || "Unknown User",
                email: data.email || "",
                isOnline: data.isOnline || false
              });
            });

            // If no members found, use mock data with all three team members
            if (membersList.length === 0) {
              const mockMembers: TeamMember[] = [
                {
                  id: "1",
                  displayName: "Pugazh",
                  email: "<EMAIL>",
                  isOnline: true
                },
                {
                  id: "2",
                  displayName: "Arun",
                  email: "<EMAIL>",
                  isOnline: true
                },
                {
                  id: "3",
                  displayName: "Tamilselvan",
                  email: "<EMAIL>",
                  isOnline: true
                }
              ];
              setMembers(mockMembers);
            } else {
              setMembers(membersList);
            }
          },
          (error) => {
            console.error('AdminMessages: Members Firestore error:', error);

            // Handle potential offline error
            if (handlePotentialOfflineError(error)) {
              console.log('AdminMessages: Members switched to offline mode');
              const mockMembers: TeamMember[] = [
                {
                  id: "1",
                  displayName: "Pugazh",
                  email: "<EMAIL>",
                  isOnline: true
                },
                {
                  id: "2",
                  displayName: "Arun",
                  email: "<EMAIL>",
                  isOnline: true
                },
                {
                  id: "3",
                  displayName: "Tamilselvan",
                  email: "<EMAIL>",
                  isOnline: true
                }
              ];
              setMembers(mockMembers);
            } else {
              setMembers([]);
            }
          }
        );

        return unsubscribe;
      } catch (error) {
        console.error('AdminMessages: Members setup error:', error);
        setMembers([]);
      }
    };

    loadMembers();
  }, []);

  const handleSendMessage = async () => {
    if (!newMessage.trim() || !user) return;

    setSending(true);
    try {
      // Check offline mode
      const offlineMode = getOfflineMode();
      if (offlineMode.isOffline) {
        toast({
          title: "Offline Mode",
          description: "Messages cannot be sent while offline. Please check your connection.",
          variant: "destructive",
        });
        setSending(false);
        return;
      }

      // Use withRetry for better reliability
      await withRetry(async () => {
        await addDoc(collection(db, "adminMessages"), {
          message: newMessage.trim(),
          sender: {
            uid: user.uid,
            displayName: user.displayName || user.email?.split('@')[0] || "Admin",
            email: user.email || ""
          },
          timestamp: serverTimestamp(),
          recipients: selectedMembers.length > 0 ? selectedMembers : members.map(m => m.id),
          priority,
          type: messageType,
          // Additional metadata
          createdAt: serverTimestamp(),
          isRead: false,
          readBy: [],
          // Track message stats
          recipientCount: selectedMembers.length > 0 ? selectedMembers.length : members.length
        });
      });

      // Clear form
      setNewMessage("");
      setSelectedMembers([]);

      // Success notification
      const recipientCount = selectedMembers.length > 0 ? selectedMembers.length : members.length;
      toast({
        title: "Message Sent Successfully! 📢",
        description: `${messageType.charAt(0).toUpperCase() + messageType.slice(1)} sent to ${recipientCount} member(s)`,
      });

      console.log('AdminMessages: Message sent successfully');
    } catch (error: any) {
      console.error('AdminMessages: Send error:', error);

      // Handle potential offline error
      if (handlePotentialOfflineError(error)) {
        toast({
          title: "Connection Lost",
          description: "Message could not be sent. App is now in offline mode.",
          variant: "destructive",
        });
      } else {
        const errorMessage = handleFirestoreError(error);
        toast({
          title: "Failed to Send Message",
          description: errorMessage,
          variant: "destructive",
        });
      }
    } finally {
      setSending(false);
    }
  };

  const toggleMemberSelection = (memberId: string) => {
    setSelectedMembers(prev =>
      prev.includes(memberId)
        ? prev.filter(id => id !== memberId)
        : [...prev, memberId]
    );
  };

  const selectAllMembers = () => {
    setSelectedMembers(members.map(m => m.id));
  };

  const clearSelection = () => {
    setSelectedMembers([]);
  };

  const formatTimestamp = (timestamp: any) => {
    if (!timestamp) return "Just now";

    try {
      const date = timestamp.toDate ? timestamp.toDate() : new Date(timestamp);
      const now = new Date();
      const diffMs = now.getTime() - date.getTime();
      const diffMins = Math.floor(diffMs / 60000);
      const diffHours = Math.floor(diffMs / 3600000);
      const diffDays = Math.floor(diffMs / 86400000);

      if (diffMins < 1) return "Just now";
      if (diffMins < 60) return `${diffMins}m ago`;
      if (diffHours < 24) return `${diffHours}h ago`;
      if (diffDays < 7) return `${diffDays}d ago`;

      return date.toLocaleDateString();
    } catch (error) {
      return "Unknown time";
    }
  };

  const getPriorityColor = (priority: string) => {
    switch (priority) {
      case "high":
        return "bg-red-500/20 text-red-300 border-red-500/50";
      case "medium":
        return "bg-yellow-500/20 text-yellow-300 border-yellow-500/50";
      case "low":
        return "bg-green-500/20 text-green-300 border-green-500/50";
      default:
        return "bg-gray-500/20 text-gray-300 border-gray-500/50";
    }
  };

  const getTypeIcon = (type: string) => {
    switch (type) {
      case "announcement":
        return "fas fa-bullhorn";
      case "alert":
        return "fas fa-exclamation-triangle";
      case "info":
        return "fas fa-info-circle";
      default:
        return "fas fa-message";
    }
  };

  if (loading) {
    return (
      <div className="glass rounded-3xl p-6 slide-up">
        <h3 className="text-xl font-bold text-white mb-4">Admin Messages</h3>
        <div className="animate-pulse space-y-4">
          {[1, 2, 3].map((i) => (
            <div key={i} className="h-20 bg-white/10 rounded-xl"></div>
          ))}
        </div>
      </div>
    );
  }

  // Check if user is admin
  const isAdmin = user?.email === "<EMAIL>" || user?.displayName?.toLowerCase().includes("admin");
  const offlineMode = getOfflineMode();

  return (
    <div className="glass rounded-3xl p-6 slide-up">
      <div className="flex items-center justify-between mb-6">
        <h3 className="text-xl font-bold text-white flex items-center">
          <i className="fas fa-bullhorn mr-2 text-orange-400 pulse-glow"></i>
          Admin Messages
          {isAdmin && (
            <span className="ml-2 text-xs bg-orange-500/20 text-orange-300 px-2 py-1 rounded-full">
              Admin
            </span>
          )}
        </h3>
        <div className="flex items-center space-x-3">
          {/* Connection Status */}
          <div className="flex items-center space-x-1">
            <div className={`w-2 h-2 rounded-full ${
              offlineMode.isOffline ? 'bg-red-400' : 'bg-green-400 animate-pulse'
            }`}></div>
            <span className="text-white/60 text-xs">
              {offlineMode.isOffline ? 'Offline' : 'Live'}
            </span>
          </div>
          {/* Member Count */}
          <div className="flex items-center space-x-1">
            <i className="fas fa-users text-white/60 text-xs"></i>
            <span className="text-white/60 text-sm">{members.length}</span>
          </div>
        </div>
      </div>

      {/* Send Message Form - Only for Admins */}
      {isAdmin ? (
        <div className="glass-dark rounded-xl p-4 mb-6">
          <div className="flex items-center mb-3">
            <i className="fas fa-edit text-orange-400 mr-2"></i>
            <span className="text-white font-medium">Send Team Message</span>
            {offlineMode.isOffline && (
              <span className="ml-2 text-xs bg-red-500/20 text-red-300 px-2 py-1 rounded-full">
                Offline - Cannot Send
              </span>
            )}
          </div>
          <div className="space-y-4">
          {/* Message Input */}
          <div>
            <Input
              placeholder="Type your message to the team..."
              value={newMessage}
              onChange={(e) => setNewMessage(e.target.value)}
              className="glass border-white/20 text-white placeholder-white/60 focus:ring-orange-500/30"
            />
          </div>

          {/* Message Options */}
          <div className="flex flex-wrap gap-2">
            <select
              value={messageType}
              onChange={(e) => setMessageType(e.target.value as any)}
              className="glass border-white/20 text-white bg-transparent rounded-lg px-3 py-2 text-sm"
            >
              <option value="announcement" className="bg-gray-800">📢 Announcement</option>
              <option value="alert" className="bg-gray-800">⚠️ Alert</option>
              <option value="info" className="bg-gray-800">ℹ️ Info</option>
            </select>

            <select
              value={priority}
              onChange={(e) => setPriority(e.target.value as any)}
              className="glass border-white/20 text-white bg-transparent rounded-lg px-3 py-2 text-sm"
            >
              <option value="low" className="bg-gray-800">🟢 Low Priority</option>
              <option value="medium" className="bg-gray-800">🟡 Medium Priority</option>
              <option value="high" className="bg-gray-800">🔴 High Priority</option>
            </select>
          </div>

          {/* Member Selection */}
          <div>
            <div className="flex items-center justify-between mb-2">
              <span className="text-white/80 text-sm">Send to:</span>
              <div className="flex space-x-2">
                <button
                  onClick={selectAllMembers}
                  className="text-blue-400 text-xs hover:text-blue-300"
                >
                  Select All
                </button>
                <button
                  onClick={clearSelection}
                  className="text-red-400 text-xs hover:text-red-300"
                >
                  Clear
                </button>
              </div>
            </div>
            <div className="grid grid-cols-2 gap-2 max-h-32 overflow-y-auto">
              {members.map((member) => (
                <label
                  key={member.id}
                  className="flex items-center space-x-2 glass rounded-lg p-2 cursor-pointer hover:bg-white/10"
                >
                  <input
                    type="checkbox"
                    checked={selectedMembers.includes(member.id)}
                    onChange={() => toggleMemberSelection(member.id)}
                    className="rounded border-white/20"
                  />
                  <div className="flex items-center space-x-2 flex-1 min-w-0">
                    <div className={`w-2 h-2 rounded-full ${member.isOnline ? 'bg-green-400' : 'bg-gray-500'}`}></div>
                    <span className="text-white text-sm truncate">{member.displayName}</span>
                  </div>
                </label>
              ))}
            </div>
            <div className="text-white/60 text-xs mt-2">
              {selectedMembers.length > 0
                ? `${selectedMembers.length} member(s) selected`
                : `All ${members.length} members will receive this message`
              }
            </div>
          </div>

          {/* Send Button */}
          <Button
            onClick={handleSendMessage}
            disabled={!newMessage.trim() || sending}
            className="w-full bg-orange-500 hover:bg-orange-600 text-white touch-feedback"
          >
            {sending ? (
              <div className="flex items-center">
                <div className="w-4 h-4 border-2 border-white/30 border-t-white rounded-full animate-spin mr-2"></div>
                Sending...
              </div>
            ) : (
              <>
                <i className="fas fa-paper-plane mr-2"></i>
                Send Message
              </>
            )}
          </Button>
          </div>
        </div>
      ) : (
        <div className="glass-dark rounded-xl p-4 mb-6 text-center">
          <i className="fas fa-lock text-white/40 text-2xl mb-2"></i>
          <p className="text-white/60 text-sm">Admin privileges required to send messages</p>
          <p className="text-white/40 text-xs">You can view messages below</p>
        </div>
      )}

      {/* Recent Messages */}
      <div className="space-y-3">
        <h4 className="text-white/80 font-medium">Recent Messages</h4>
        {messages.length === 0 ? (
          <div className="text-center py-8">
            <i className="fas fa-inbox text-4xl text-white/20 mb-4"></i>
            <p className="text-white/60">No messages sent yet</p>
            <p className="text-white/40 text-sm">Send your first team message above</p>
          </div>
        ) : (
          messages.slice(0, 5).map((message) => (
            <div
              key={message.id}
              className={`glass-dark rounded-xl p-4 border ${getPriorityColor(message.priority)}`}
            >
              <div className="flex items-start space-x-3">
                <div className="w-8 h-8 glass rounded-lg flex items-center justify-center">
                  <i className={`${getTypeIcon(message.type)} text-sm`}></i>
                </div>
                <div className="flex-1">
                  <div className="flex items-center space-x-2 mb-1">
                    <span className="text-white font-medium text-sm">
                      {message.sender.displayName}
                    </span>
                    <span className={`text-xs px-2 py-1 rounded-full ${getPriorityColor(message.priority)}`}>
                      {message.priority}
                    </span>
                  </div>
                  <p className="text-white/80 text-sm mb-2">{message.message}</p>
                  <div className="flex items-center space-x-4 text-xs text-white/50">
                    <span>{formatTimestamp(message.timestamp)}</span>
                    <span>{message.recipients.length} recipient(s)</span>
                  </div>
                </div>
              </div>
            </div>
          ))
        )}
      </div>
    </div>
  );
}


