// Local storage backup system for offline functionality

export interface Task {
  id: string;
  text: string;
  completed: boolean;
  priority: 'low' | 'medium' | 'high';
  dueDate?: string;
  createdAt: string;
  userId: string;
}

export interface User {
  uid: string;
  displayName: string;
  email: string;
  photoURL?: string;
  role: string;
  isOnline: boolean;
  lastSeen: string;
  tasksCompleted: number;
}

const TASKS_KEY = 'taskmaster_tasks';
const USERS_KEY = 'taskmaster_users';
const MESSAGES_KEY = 'taskmaster_messages';

// Tasks
export const saveTasksToLocal = (tasks: Task[]) => {
  try {
    localStorage.setItem(TASKS_KEY, JSON.stringify(tasks));
  } catch (error) {
    console.error('Error saving tasks to localStorage:', error);
  }
};

export const getTasksFromLocal = (): Task[] => {
  try {
    const tasks = localStorage.getItem(TASKS_KEY);
    return tasks ? JSON.parse(tasks) : [];
  } catch (error) {
    console.error('Error loading tasks from localStorage:', error);
    return [];
  }
};

// Users
export const saveUsersToLocal = (users: User[]) => {
  try {
    localStorage.setItem(USERS_KEY, JSON.stringify(users));
  } catch (error) {
    console.error('Error saving users to localStorage:', error);
  }
};

export const getUsersFromLocal = (): User[] => {
  try {
    const users = localStorage.getItem(USERS_KEY);
    return users ? JSON.parse(users) : [];
  } catch (error) {
    console.error('Error loading users from localStorage:', error);
    return [];
  }
};

// Messages
export const saveMessagesToLocal = (messages: any[]) => {
  try {
    localStorage.setItem(MESSAGES_KEY, JSON.stringify(messages));
  } catch (error) {
    console.error('Error saving messages to localStorage:', error);
  }
};

export const getMessagesFromLocal = (): any[] => {
  try {
    const messages = localStorage.getItem(MESSAGES_KEY);
    return messages ? JSON.parse(messages) : [];
  } catch (error) {
    console.error('Error loading messages from localStorage:', error);
    return [];
  }
};

// Sync status
export const setSyncStatus = (status: 'synced' | 'pending' | 'offline') => {
  try {
    localStorage.setItem('taskmaster_sync_status', status);
  } catch (error) {
    console.error('Error saving sync status:', error);
  }
};

export const getSyncStatus = (): 'synced' | 'pending' | 'offline' => {
  try {
    const status = localStorage.getItem('taskmaster_sync_status');
    return (status as any) || 'offline';
  } catch (error) {
    console.error('Error loading sync status:', error);
    return 'offline';
  }
};

// Clear all local data
export const clearLocalData = () => {
  try {
    localStorage.removeItem(TASKS_KEY);
    localStorage.removeItem(USERS_KEY);
    localStorage.removeItem(MESSAGES_KEY);
    localStorage.removeItem('taskmaster_sync_status');
  } catch (error) {
    console.error('Error clearing local data:', error);
  }
};

// Check if we have local data
export const hasLocalData = () => {
  try {
    return !!(localStorage.getItem(TASKS_KEY) ||
              localStorage.getItem(USERS_KEY) ||
              localStorage.getItem(MESSAGES_KEY));
  } catch (error) {
    return false;
  }
};
