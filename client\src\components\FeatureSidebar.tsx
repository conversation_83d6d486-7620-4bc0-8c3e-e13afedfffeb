import { useState } from "react";
import { Button } from "@/components/ui/button";

interface FeatureSidebarProps {
  isOpen: boolean;
  onClose: () => void;
}

export default function FeatureSidebar({ isOpen, onClose }: FeatureSidebarProps) {
  const [activeFeature, setActiveFeature] = useState("calendar");

  const features = [
    {
      id: "calendar",
      name: "Calendar",
      icon: "fas fa-calendar-alt",
      color: "text-blue-400",
      description: "View tasks in calendar format"
    },
    {
      id: "analytics",
      name: "Analytics",
      icon: "fas fa-chart-line",
      color: "text-green-400",
      description: "Detailed productivity insights"
    },
    {
      id: "timer",
      name: "Pomodoro Timer",
      icon: "fas fa-clock",
      color: "text-red-400",
      description: "Focus timer for productivity"
    },
    {
      id: "notes",
      name: "Quick Notes",
      icon: "fas fa-sticky-note",
      color: "text-yellow-400",
      description: "Jot down quick thoughts"
    },
    {
      id: "goals",
      name: "Goals",
      icon: "fas fa-bullseye",
      color: "text-purple-400",
      description: "Set and track your goals"
    },
    {
      id: "habits",
      name: "Habit Tracker",
      icon: "fas fa-check-double",
      color: "text-pink-400",
      description: "Build and maintain habits"
    }
  ];

  if (!isOpen) return null;

  return (
    <div className="fixed inset-0 bg-black/70 backdrop-blur-sm z-50 fade-in">
      <div className="fixed right-0 top-0 h-full w-80 sm:w-96 glass-dark border-l-2 border-purple-500/30 slide-up">
        {/* Header */}
        <div className="flex items-center justify-between p-4 border-b border-white/10">
          <h2 className="text-xl font-bold text-white flex items-center">
            <i className="fas fa-magic mr-2 text-purple-400 pulse-glow"></i>
            Features
          </h2>
          <button
            onClick={onClose}
            className="close-btn touch-target"
            title="Close Features"
            aria-label="Close Features"
          >
            <i className="fas fa-times"></i>
          </button>
        </div>

        {/* Feature Grid */}
        <div className="p-4 space-y-3 max-h-[calc(100vh-80px)] overflow-y-auto">
          {features.map((feature) => (
            <div
              key={feature.id}
              onClick={() => setActiveFeature(feature.id)}
              className={`glass rounded-xl p-4 cursor-pointer touch-feedback transition-all duration-200 ${
                activeFeature === feature.id 
                  ? 'bg-white/20 border border-purple-500/50' 
                  : 'hover:bg-white/10'
              }`}
            >
              <div className="flex items-center space-x-3">
                <div className={`w-10 h-10 glass rounded-lg flex items-center justify-center ${feature.color}`}>
                  <i className={feature.icon}></i>
                </div>
                <div className="flex-1">
                  <h3 className="text-white font-medium">{feature.name}</h3>
                  <p className="text-white/60 text-sm">{feature.description}</p>
                </div>
                <div className="flex items-center space-x-2">
                  {feature.id === "timer" && (
                    <div className="w-2 h-2 bg-red-400 rounded-full animate-pulse"></div>
                  )}
                  <i className="fas fa-chevron-right text-white/40"></i>
                </div>
              </div>

              {/* Feature Content */}
              {activeFeature === feature.id && (
                <div className="mt-4 pt-4 border-t border-white/10 slide-up">
                  {feature.id === "calendar" && (
                    <div className="space-y-3">
                      <div className="text-white/80 text-sm">Upcoming Tasks</div>
                      <div className="space-y-2">
                        <div className="glass-dark rounded-lg p-3 flex items-center justify-between">
                          <span className="text-white text-sm">Team Meeting</span>
                          <span className="text-blue-400 text-xs">Today 2:00 PM</span>
                        </div>
                        <div className="glass-dark rounded-lg p-3 flex items-center justify-between">
                          <span className="text-white text-sm">Project Review</span>
                          <span className="text-yellow-400 text-xs">Tomorrow 10:00 AM</span>
                        </div>
                      </div>
                    </div>
                  )}

                  {feature.id === "timer" && (
                    <div className="text-center space-y-4">
                      <div className="text-3xl font-bold text-white">25:00</div>
                      <div className="flex justify-center space-x-2">
                        <Button className="bg-green-500 hover:bg-green-600 text-white px-4 py-2 text-sm touch-feedback">
                          <i className="fas fa-play mr-1"></i>Start
                        </Button>
                        <Button className="bg-red-500 hover:bg-red-600 text-white px-4 py-2 text-sm touch-feedback">
                          <i className="fas fa-stop mr-1"></i>Stop
                        </Button>
                      </div>
                    </div>
                  )}

                  {feature.id === "notes" && (
                    <div className="space-y-3">
                      <textarea
                        placeholder="Quick note..."
                        className="w-full glass border-white/20 text-white placeholder-white/60 rounded-lg p-3 text-sm resize-none"
                        rows={3}
                      />
                      <Button className="w-full bg-yellow-500 hover:bg-yellow-600 text-black text-sm touch-feedback">
                        <i className="fas fa-save mr-1"></i>Save Note
                      </Button>
                    </div>
                  )}

                  {feature.id === "analytics" && (
                    <div className="space-y-3">
                      <div className="grid grid-cols-2 gap-2">
                        <div className="glass-dark rounded-lg p-3 text-center">
                          <div className="text-lg font-bold text-green-400">85%</div>
                          <div className="text-xs text-white/60">Completion</div>
                        </div>
                        <div className="glass-dark rounded-lg p-3 text-center">
                          <div className="text-lg font-bold text-blue-400">12</div>
                          <div className="text-xs text-white/60">Streak Days</div>
                        </div>
                      </div>
                    </div>
                  )}

                  {feature.id === "goals" && (
                    <div className="space-y-3">
                      <div className="glass-dark rounded-lg p-3">
                        <div className="flex items-center justify-between mb-2">
                          <span className="text-white text-sm">Complete 50 Tasks</span>
                          <span className="text-purple-400 text-xs">32/50</span>
                        </div>
                        <div className="w-full glass rounded-full h-2">
                          <div className="bg-purple-500 h-2 rounded-full" style={{ width: '64%' }}></div>
                        </div>
                      </div>
                    </div>
                  )}

                  {feature.id === "habits" && (
                    <div className="space-y-2">
                      <div className="glass-dark rounded-lg p-3 flex items-center justify-between">
                        <span className="text-white text-sm">Daily Exercise</span>
                        <div className="flex space-x-1">
                          {[1,2,3,4,5,6,7].map(day => (
                            <div key={day} className={`w-3 h-3 rounded-full ${day <= 4 ? 'bg-green-400' : 'bg-white/20'}`}></div>
                          ))}
                        </div>
                      </div>
                      <div className="glass-dark rounded-lg p-3 flex items-center justify-between">
                        <span className="text-white text-sm">Read 30 min</span>
                        <div className="flex space-x-1">
                          {[1,2,3,4,5,6,7].map(day => (
                            <div key={day} className={`w-3 h-3 rounded-full ${day <= 6 ? 'bg-green-400' : 'bg-white/20'}`}></div>
                          ))}
                        </div>
                      </div>
                    </div>
                  )}
                </div>
              )}
            </div>
          ))}
        </div>
      </div>
    </div>
  );
}
