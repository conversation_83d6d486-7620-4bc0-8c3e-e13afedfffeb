// Offline mode utilities to handle Firebase connection issues

import {
  saveTasksToLocal,
  getTasksFromLocal,
  saveUsersToLocal,
  getUsersFromLocal,
  setSyncStatus,
  getSyncStatus
} from './localStorageBackup';

// Mock data for offline mode
export const mockTasks = [
  {
    id: '1',
    text: 'Welcome to Cyber Task!',
    completed: false,
    priority: 'high' as const,
    dueDate: new Date(Date.now() + 86400000).toISOString(), // Tomorrow
    createdAt: new Date().toISOString(),
    userId: 'offline-user'
  },
  {
    id: '2',
    text: 'Complete project documentation',
    completed: false,
    priority: 'medium' as const,
    dueDate: new Date(Date.now() + 172800000).toISOString(), // Day after tomorrow
    createdAt: new Date().toISOString(),
    userId: 'offline-user'
  },
  {
    id: '3',
    text: 'Review team performance',
    completed: true,
    priority: 'low' as const,
    createdAt: new Date(Date.now() - 86400000).toISOString(), // Yesterday
    userId: 'offline-user'
  }
];

export const mockUsers = [
  {
    uid: 'offline-user',
    displayName: 'You (Offline)',
    email: '<EMAIL>',
    role: 'Admin',
    isOnline: true,
    lastSeen: new Date().toISOString(),
    tasksCompleted: 15
  },
  {
    uid: 'user-1',
    displayName: 'Tamizh',
    email: '<EMAIL>',
    role: 'Lead',
    isOnline: true,
    lastSeen: new Date().toISOString(),
    tasksCompleted: 45
  },
  {
    uid: 'user-2',
    displayName: 'Pugazh',
    email: '<EMAIL>',
    role: 'Developer',
    isOnline: true,
    lastSeen: new Date(Date.now() - 300000).toISOString(), // 5 minutes ago
    tasksCompleted: 38
  }
];

// Offline mode state
let isOfflineMode = false;
let offlineModeReason = '';

export const setOfflineMode = (offline: boolean, reason?: string) => {
  isOfflineMode = offline;
  offlineModeReason = reason || '';
  setSyncStatus(offline ? 'offline' : 'synced');

  if (offline) {
    console.log('🔌 Switching to offline mode:', reason);
    // Save mock data to local storage
    saveTasksToLocal(mockTasks);
    saveUsersToLocal(mockUsers);
  } else {
    console.log('🌐 Switching to online mode');
  }
};

export const getOfflineMode = () => ({
  isOffline: isOfflineMode,
  reason: offlineModeReason
});

// Initialize offline mode if needed
export const initializeOfflineMode = () => {
  const syncStatus = getSyncStatus();
  if (syncStatus === 'offline') {
    setOfflineMode(true, 'Previously offline');
  }
};

// Offline-first data access
export const getTasksOfflineFirst = () => {
  if (isOfflineMode) {
    return getTasksFromLocal();
  }
  // Try to get from local storage first, then fallback to mock
  const localTasks = getTasksFromLocal();
  return localTasks.length > 0 ? localTasks : mockTasks;
};

export const getUsersOfflineFirst = () => {
  if (isOfflineMode) {
    return getUsersFromLocal();
  }
  // Try to get from local storage first, then fallback to mock
  const localUsers = getUsersFromLocal();
  return localUsers.length > 0 ? localUsers : mockUsers;
};

// Simulate network delay for better UX
export const simulateNetworkDelay = (ms: number = 500) => {
  return new Promise(resolve => setTimeout(resolve, ms));
};

// Check if we should use offline mode
export const shouldUseOfflineMode = (error?: any): boolean => {
  if (!error) return false;

  // Check for specific Firebase/network errors
  const offlineErrors = [
    'unavailable',
    'deadline-exceeded',
    'aborted',
    'network-request-failed',
    'Failed to fetch'
  ];

  return offlineErrors.some(errorType =>
    error.code === errorType ||
    error.message?.includes(errorType) ||
    error.toString().includes(errorType)
  );
};

// Auto-detect offline mode based on errors
export const handlePotentialOfflineError = (error: any) => {
  if (shouldUseOfflineMode(error)) {
    setOfflineMode(true, `Connection error: ${error.message || error.code}`);
    return true;
  }
  return false;
};

// Initialize on module load
if (typeof window !== 'undefined') {
  initializeOfflineMode();
}
