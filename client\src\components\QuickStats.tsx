import { useRealtimeTasks } from "@/hooks/useRealtimeDatabase";

export default function QuickStats() {
  const { tasks, loading } = useRealtimeTasks();

  const stats = {
    totalTasks: tasks.length,
    completedTasks: tasks.filter(task => task.status === "completed").length,
    inProgressTasks: tasks.filter(task => task.status === "active").length,
    overdueTasks: tasks.filter(task => {
      if (!task.dueDate || task.status === "completed") return false;
      const dueDate = task.dueDate.toDate ? task.dueDate.toDate() : new Date(task.dueDate);
      return dueDate < new Date();
    }).length,
  };

  const completionPercentage = stats.totalTasks > 0
    ? Math.round((stats.completedTasks / stats.totalTasks) * 100)
    : 0;

  if (loading) {
    return (
      <div className="glass rounded-3xl p-6">
        <h3 className="text-xl font-bold text-white mb-4">Quick Stats</h3>
        <div className="animate-pulse space-y-4">
          {[1, 2, 3, 4].map((i) => (
            <div key={i} className="flex justify-between">
              <div className="h-4 bg-white/10 rounded w-1/2"></div>
              <div className="h-4 bg-white/10 rounded w-8"></div>
            </div>
          ))}
        </div>
      </div>
    );
  }

  return (
    <div className="glass rounded-2xl sm:rounded-3xl p-4 sm:p-6 slide-up">
      {/* Header - responsive */}
      <div className="flex items-center justify-between mb-4 sm:mb-6">
        <h3 className="text-lg sm:text-xl font-bold text-white flex items-center">
          <i className="fas fa-chart-pie mr-2 text-purple-400 pulse-glow text-sm sm:text-base"></i>
          <span className="hidden sm:inline">Quick Stats</span>
          <span className="sm:hidden">Stats</span>
        </h3>
        <div className="w-2 h-2 bg-green-400 rounded-full animate-pulse"></div>
      </div>

      {/* Stats grid - responsive layout */}
      <div className="grid grid-cols-2 gap-2 sm:gap-4 mb-4 sm:mb-6">
        <div className="glass-dark rounded-lg sm:rounded-xl p-3 sm:p-4 text-center touch-feedback">
          <div className="text-lg sm:text-2xl font-bold text-white mb-1">{stats.totalTasks}</div>
          <div className="text-white/60 text-xs sm:text-sm flex items-center justify-center">
            <i className="fas fa-tasks mr-1 text-xs sm:text-sm"></i>
            Total
          </div>
        </div>
        <div className="glass-dark rounded-lg sm:rounded-xl p-3 sm:p-4 text-center touch-feedback">
          <div className="text-lg sm:text-2xl font-bold text-green-400 mb-1">{stats.completedTasks}</div>
          <div className="text-white/60 text-xs sm:text-sm flex items-center justify-center">
            <i className="fas fa-check-circle mr-1 text-xs sm:text-sm"></i>
            Done
          </div>
        </div>
        <div className="glass-dark rounded-lg sm:rounded-xl p-3 sm:p-4 text-center touch-feedback">
          <div className="text-lg sm:text-2xl font-bold text-yellow-400 mb-1">{stats.inProgressTasks}</div>
          <div className="text-white/60 text-xs sm:text-sm flex items-center justify-center">
            <i className="fas fa-clock mr-1 text-xs sm:text-sm"></i>
            Active
          </div>
        </div>
        <div className="glass-dark rounded-lg sm:rounded-xl p-3 sm:p-4 text-center touch-feedback">
          <div className="text-lg sm:text-2xl font-bold text-red-400 mb-1">{stats.overdueTasks}</div>
          <div className="text-white/60 text-xs sm:text-sm flex items-center justify-center">
            <i className="fas fa-exclamation-triangle mr-1 text-xs sm:text-sm"></i>
            Overdue
          </div>
        </div>
      </div>

      {/* Progress and productivity sections - responsive */}
      <div className="space-y-3 sm:space-y-4">
        <div className="glass-dark rounded-lg sm:rounded-xl p-3 sm:p-4">
          <div className="flex items-center justify-between text-xs sm:text-sm text-white/60 mb-3">
            <span className="flex items-center">
              <i className="fas fa-chart-line mr-2 text-xs sm:text-sm"></i>
              <span className="hidden sm:inline">Overall Progress</span>
              <span className="sm:hidden">Progress</span>
            </span>
            <span className="text-white font-bold text-sm sm:text-base">{completionPercentage}%</span>
          </div>
          <div className="w-full glass rounded-full h-2 sm:h-3 overflow-hidden">
            <div
              className="bg-gradient-to-r from-purple-500 to-pink-500 h-2 sm:h-3 rounded-full transition-all duration-1000 ease-out pulse-glow"
              style={{ width: `${completionPercentage}%` }}
            ></div>
          </div>
        </div>

        {/* Productivity Score - responsive */}
        <div className="glass-dark rounded-lg sm:rounded-xl p-3 sm:p-4">
          <div className="flex items-center justify-between">
            <div className="flex items-center">
              <i className="fas fa-trophy text-yellow-400 mr-2 text-xs sm:text-sm"></i>
              <span className="text-white/80 text-xs sm:text-sm">
                <span className="hidden sm:inline">Productivity Score</span>
                <span className="sm:hidden">Score</span>
              </span>
            </div>
            <div className="text-right">
              <div className="text-base sm:text-lg font-bold text-yellow-400">
                {Math.max(0, Math.min(100, completionPercentage + (stats.totalTasks * 5)))}
              </div>
              <div className="text-xs text-white/50">
                {completionPercentage >= 80 ? 'Excellent!' :
                 completionPercentage >= 60 ? 'Good' :
                 completionPercentage >= 40 ? 'Fair' : 'Keep going!'}
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>
  );
}
