import { useAuth } from "@/hooks/useAuth";
import AuthScreen from "@/components/AuthScreen";
import MainApp from "@/components/MainApp";

export default function Home() {
  const { user, loading } = useAuth();

  console.log('Home: Rendering with user:', user ? `logged in as ${user.email}` : 'not logged in', 'loading:', loading);
  console.log('Home: User object:', user);
  console.log('Home: Will render:', user ? 'MainApp' : 'AuthScreen');

  // Skip all loading states and go directly to auth/main app
  if (user) {
    console.log('Home: Rendering MainApp for user:', user.email);
    return <MainApp />;
  } else {
    console.log('Home: Rendering AuthScreen - no user found');
    return <AuthScreen />;
  }
}
