import { useState, useEffect } from "react";
import { But<PERSON> } from "@/components/ui/button";
import { Input } from "@/components/ui/input";

interface SearchModalProps {
  isOpen: boolean;
  onClose: () => void;
}

export default function SearchModal({ isOpen, onClose }: SearchModalProps) {
  const [searchQuery, setSearchQuery] = useState("");
  const [searchResults, setSearchResults] = useState<any[]>([]);
  const [recentSearches, setRecentSearches] = useState([
    "High priority tasks",
    "Overdue items",
    "Team meetings",
    "Project deadlines"
  ]);

  // Mock search results
  const mockResults = [
    {
      id: 1,
      type: "task",
      title: "Complete project proposal",
      description: "Finish the Q4 project proposal document",
      priority: "high",
      icon: "fas fa-tasks"
    },
    {
      id: 2,
      type: "message",
      title: "Team meeting reminder",
      description: "Don't forget about the 2 PM team sync",
      priority: "medium",
      icon: "fas fa-envelope"
    },
    {
      id: 3,
      type: "user",
      title: "<PERSON>",
      description: "Project Manager - Available",
      priority: "low",
      icon: "fas fa-user"
    }
  ];

  useEffect(() => {
    if (searchQuery.length > 2) {
      // Simulate search delay
      const timer = setTimeout(() => {
        setSearchResults(mockResults.filter(item => 
          item.title.toLowerCase().includes(searchQuery.toLowerCase()) ||
          item.description.toLowerCase().includes(searchQuery.toLowerCase())
        ));
      }, 300);
      return () => clearTimeout(timer);
    } else {
      setSearchResults([]);
    }
  }, [searchQuery]);

  // Handle ESC key
  useEffect(() => {
    const handleEscKey = (event: KeyboardEvent) => {
      if (event.key === 'Escape') {
        onClose();
      }
    };

    if (isOpen) {
      document.addEventListener('keydown', handleEscKey);
      return () => {
        document.removeEventListener('keydown', handleEscKey);
      };
    }
  }, [isOpen, onClose]);

  if (!isOpen) return null;

  return (
    <div 
      className="fixed inset-0 bg-black/70 backdrop-blur-sm z-50 flex items-start justify-center pt-20 fade-in"
      onClick={onClose}
    >
      <div 
        className="glass rounded-2xl w-full max-w-2xl mx-4 bounce-in"
        onClick={(e) => e.stopPropagation()}
      >
        {/* Header */}
        <div className="flex items-center justify-between p-4 border-b border-white/10">
          <div className="flex items-center space-x-3">
            <i className="fas fa-search text-purple-400 pulse-glow"></i>
            <h2 className="text-lg font-bold text-white">Search Everything</h2>
          </div>
          <button
            onClick={onClose}
            className="close-btn touch-target"
            title="Close Search"
            aria-label="Close Search"
          >
            <i className="fas fa-times"></i>
          </button>
        </div>

        {/* Search Input */}
        <div className="p-4">
          <div className="relative">
            <Input
              type="text"
              placeholder="Search tasks, messages, users..."
              value={searchQuery}
              onChange={(e) => setSearchQuery(e.target.value)}
              className="glass border-white/20 text-white placeholder-white/60 focus:ring-purple-500/30 focus:border-purple-500/40 pl-10"
              autoFocus
            />
            <i className="fas fa-search absolute left-3 top-1/2 transform -translate-y-1/2 text-white/60"></i>
          </div>
        </div>

        {/* Search Results */}
        <div className="max-h-96 overflow-y-auto">
          {searchQuery.length > 2 ? (
            searchResults.length > 0 ? (
              <div className="p-4 space-y-2">
                <div className="text-white/60 text-sm mb-3">
                  Found {searchResults.length} result{searchResults.length !== 1 ? 's' : ''}
                </div>
                {searchResults.map((result) => (
                  <div
                    key={result.id}
                    className="glass-dark rounded-lg p-3 hover:bg-white/10 cursor-pointer touch-feedback transition-all duration-200"
                  >
                    <div className="flex items-center space-x-3">
                      <div className={`w-8 h-8 glass rounded-lg flex items-center justify-center ${
                        result.type === 'task' ? 'text-blue-400' :
                        result.type === 'message' ? 'text-green-400' :
                        'text-purple-400'
                      }`}>
                        <i className={result.icon}></i>
                      </div>
                      <div className="flex-1">
                        <h3 className="text-white font-medium text-sm">{result.title}</h3>
                        <p className="text-white/60 text-xs">{result.description}</p>
                      </div>
                      <div className={`px-2 py-1 rounded-full text-xs ${
                        result.priority === 'high' ? 'bg-red-500/20 text-red-400' :
                        result.priority === 'medium' ? 'bg-yellow-500/20 text-yellow-400' :
                        'bg-green-500/20 text-green-400'
                      }`}>
                        {result.priority}
                      </div>
                    </div>
                  </div>
                ))}
              </div>
            ) : (
              <div className="p-8 text-center">
                <i className="fas fa-search text-white/40 text-3xl mb-3"></i>
                <div className="text-white/60">No results found for "{searchQuery}"</div>
                <div className="text-white/40 text-sm mt-1">Try different keywords</div>
              </div>
            )
          ) : (
            <div className="p-4">
              {/* Recent Searches */}
              <div className="mb-4">
                <div className="text-white/60 text-sm mb-3 flex items-center">
                  <i className="fas fa-history mr-2"></i>
                  Recent Searches
                </div>
                <div className="space-y-2">
                  {recentSearches.map((search, index) => (
                    <button
                      key={index}
                      onClick={() => setSearchQuery(search)}
                      className="w-full text-left glass-dark rounded-lg p-3 hover:bg-white/10 touch-feedback transition-all duration-200 flex items-center space-x-3"
                    >
                      <i className="fas fa-clock text-white/40"></i>
                      <span className="text-white/80 text-sm">{search}</span>
                    </button>
                  ))}
                </div>
              </div>

              {/* Quick Actions */}
              <div>
                <div className="text-white/60 text-sm mb-3 flex items-center">
                  <i className="fas fa-bolt mr-2"></i>
                  Quick Actions
                </div>
                <div className="grid grid-cols-2 gap-2">
                  <button className="glass-dark rounded-lg p-3 hover:bg-white/10 touch-feedback transition-all duration-200 text-center">
                    <i className="fas fa-plus text-blue-400 mb-1"></i>
                    <div className="text-white text-xs">New Task</div>
                  </button>
                  <button className="glass-dark rounded-lg p-3 hover:bg-white/10 touch-feedback transition-all duration-200 text-center">
                    <i className="fas fa-filter text-green-400 mb-1"></i>
                    <div className="text-white text-xs">Filter Tasks</div>
                  </button>
                  <button className="glass-dark rounded-lg p-3 hover:bg-white/10 touch-feedback transition-all duration-200 text-center">
                    <i className="fas fa-chart-bar text-purple-400 mb-1"></i>
                    <div className="text-white text-xs">View Stats</div>
                  </button>
                  <button className="glass-dark rounded-lg p-3 hover:bg-white/10 touch-feedback transition-all duration-200 text-center">
                    <i className="fas fa-cog text-yellow-400 mb-1"></i>
                    <div className="text-white text-xs">Settings</div>
                  </button>
                </div>
              </div>
            </div>
          )}
        </div>

        {/* Footer */}
        <div className="p-4 border-t border-white/10">
          <div className="flex items-center justify-between text-xs text-white/40">
            <div className="flex items-center space-x-4">
              <span>↑↓ Navigate</span>
              <span>↵ Select</span>
              <span>Esc Close</span>
            </div>
            <div className="flex items-center space-x-1">
              <i className="fas fa-keyboard"></i>
              <span>Ctrl+K</span>
            </div>
          </div>
        </div>
      </div>
    </div>
  );
}
