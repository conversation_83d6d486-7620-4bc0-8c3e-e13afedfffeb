import { useState } from "react";
import { signInWithGoogle, signUpWithEmail, signInWithEmail, auth } from "@/lib/firebase";
import { Button } from "@/components/ui/button";
import { Input } from "@/components/ui/input";
import { useToast } from "@/hooks/use-toast";
import { useAuth } from "@/hooks/useAuth";

export default function AuthScreen() {
  const [loading, setLoading] = useState(false);
  const [isSignUp, setIsSignUp] = useState(false);
  const [formData, setFormData] = useState({
    email: "",
    password: "",
    displayName: ""
  });
  const { toast } = useToast();
  const { user } = useAuth();

  // Debug function to check current auth state
  const checkAuthState = () => {
    console.log('AuthScreen: Current auth state check');
    console.log('AuthScreen: auth.currentUser:', auth.currentUser);
    console.log('AuthScreen: useAuth user:', user);
    toast({
      title: "Auth State Debug",
      description: `Firebase: ${auth.currentUser ? 'Logged in' : 'Not logged in'}, Hook: ${user ? 'Logged in' : 'Not logged in'}`,
    });
  };

  // Test login with demo credentials
  const handleTestLogin = async () => {
    try {
      setLoading(true);
      console.log('AuthScreen: Starting test login...');

      // Try to sign in with test credentials
      const result = await signInWithEmail("<EMAIL>", "test123");
      console.log('AuthScreen: Test login successful:', result);

      toast({
        title: "Test Login Successful!",
        description: "Logged in with test account.",
      });
    } catch (error: any) {
      console.log('AuthScreen: Test login failed, trying to create test account...');

      // If login fails, try to create the test account
      try {
        const result = await signUpWithEmail("<EMAIL>", "test123", "Test User");
        console.log('AuthScreen: Test account created and logged in:', result);

        toast({
          title: "Test Account Created!",
          description: "Created and logged in with test account.",
        });
      } catch (signUpError: any) {
        console.error('AuthScreen: Test account creation failed:', signUpError);
        toast({
          title: "Test Login Failed",
          description: "Could not create or login to test account.",
          variant: "destructive",
        });
      }
    } finally {
      setLoading(false);
    }
  };

  const handleGoogleSignIn = async () => {
    try {
      setLoading(true);
      console.log('AuthScreen: Starting Google sign in...');
      const result = await signInWithGoogle();
      console.log('AuthScreen: Google sign in successful:', result);
    } catch (error: any) {
      console.error('AuthScreen: Google sign in error:', error);
      toast({
        title: "Authentication Error",
        description: error.message || "Failed to sign in with Google. Please try again.",
        variant: "destructive",
      });
    } finally {
      setLoading(false);
    }
  };

  const handleEmailAuth = async (e: React.FormEvent) => {
    e.preventDefault();
    if (!formData.email || !formData.password) return;
    if (isSignUp && !formData.displayName) return;

    try {
      setLoading(true);
      console.log('AuthScreen: Starting email auth...', isSignUp ? 'Sign Up' : 'Sign In');

      if (isSignUp) {
        const result = await signUpWithEmail(formData.email, formData.password, formData.displayName);
        console.log('AuthScreen: Sign up successful:', result);
        toast({
          title: "Account Created!",
          description: "Welcome to TaskMaster Pro! You can now start managing your tasks.",
        });
      } else {
        const result = await signInWithEmail(formData.email, formData.password);
        console.log('AuthScreen: Sign in successful:', result);
        toast({
          title: "Welcome Back!",
          description: "Successfully signed in to TaskMaster Pro.",
        });
      }
    } catch (error: any) {
      let errorMessage = "An error occurred. Please try again.";

      if (error.code === "auth/email-already-in-use") {
        errorMessage = "This email is already registered. Please sign in instead.";
      } else if (error.code === "auth/weak-password") {
        errorMessage = "Password should be at least 6 characters long.";
      } else if (error.code === "auth/invalid-email") {
        errorMessage = "Please enter a valid email address.";
      } else if (error.code === "auth/user-not-found") {
        errorMessage = "No account found with this email. Please sign up first.";
      } else if (error.code === "auth/wrong-password") {
        errorMessage = "Incorrect password. Please try again.";
      }

      toast({
        title: isSignUp ? "Sign Up Failed" : "Sign In Failed",
        description: errorMessage,
        variant: "destructive",
      });
    } finally {
      setLoading(false);
    }
  };

  return (
    <div className="min-h-screen flex items-center justify-center p-4 relative overflow-hidden">
      {/* Animated Background Elements */}
      <div className="absolute inset-0 overflow-hidden">
        <div className="absolute top-1/4 left-1/4 w-64 h-64 bg-purple-500/10 rounded-full blur-3xl animate-pulse"></div>
        <div className="absolute bottom-1/4 right-1/4 w-96 h-96 bg-pink-500/10 rounded-full blur-3xl animate-pulse delay-1000"></div>
        <div className="absolute top-1/2 left-1/2 transform -translate-x-1/2 -translate-y-1/2 w-80 h-80 bg-blue-500/5 rounded-full blur-3xl animate-pulse delay-500"></div>
      </div>

      <div className="glass blocky-card p-6 sm:p-8 w-full max-w-md smooth-transition bounce-in relative z-10">
        <div className="text-center mb-8">
          <div className="w-16 h-16 glass blocky flex items-center justify-center mx-auto mb-4 pulse-glow">
            <i className="fas fa-rocket text-2xl text-white"></i>
          </div>
          <h1 className="text-2xl sm:text-3xl font-bold text-white mb-2 slide-up">TaskMaster Pro</h1>
          <p className="text-white/70 slide-up">Advanced Task Management Platform</p>
          <div className="flex items-center justify-center space-x-2 mt-3">
            <div className="w-2 h-2 bg-green-400 rounded-full animate-pulse"></div>
            <span className="text-green-400 text-sm font-medium">Live & Secure</span>
          </div>
        </div>

        <form onSubmit={handleEmailAuth} className="space-y-4 mb-6">
          {isSignUp && (
            <div className="slide-up">
              <Input
                type="text"
                placeholder="Full Name"
                value={formData.displayName}
                onChange={(e) => setFormData({ ...formData, displayName: e.target.value })}
                className="glass border-white/20 text-white placeholder-white/60 focus:ring-white/30 focus:border-white/40 transition-all duration-200 touch-feedback"
                required
              />
            </div>
          )}
          <div className="slide-up">
            <Input
              type="email"
              placeholder="Email Address"
              value={formData.email}
              onChange={(e) => setFormData({ ...formData, email: e.target.value })}
              className="glass border-white/20 text-white placeholder-white/60 focus:ring-white/30 focus:border-white/40 transition-all duration-200 touch-feedback"
              required
            />
          </div>
          <div className="slide-up">
            <Input
              type="password"
              placeholder="Password (min 6 characters)"
              value={formData.password}
              onChange={(e) => setFormData({ ...formData, password: e.target.value })}
              className="glass border-white/20 text-white placeholder-white/60 focus:ring-white/30 focus:border-white/40 transition-all duration-200 touch-feedback"
              required
              minLength={6}
            />
          </div>

          <Button
            type="submit"
            className="w-full bg-gradient-to-r from-purple-600 to-pink-600 hover:from-purple-700 hover:to-pink-700 text-white font-semibold backdrop-blur-sm touch-feedback pulse-glow transition-all duration-200"
            disabled={loading}
          >
            {loading ? (
              <div className="flex items-center justify-center">
                <div className="w-4 h-4 border-2 border-white/30 border-t-white rounded-full animate-spin mr-2"></div>
                {isSignUp ? "Creating Account..." : "Signing In..."}
              </div>
            ) : (
              <>
                <i className={`${isSignUp ? 'fas fa-user-plus' : 'fas fa-sign-in-alt'} mr-2`}></i>
                {isSignUp ? "Create Account" : "Sign In"}
              </>
            )}
          </Button>
        </form>

        <div className="space-y-3 slide-up">
          <div className="flex items-center">
            <div className="flex-1 border-t border-white/20"></div>
            <span className="px-4 text-white/60 text-sm">or continue with</span>
            <div className="flex-1 border-t border-white/20"></div>
          </div>

          <Button
            onClick={handleGoogleSignIn}
            disabled={loading}
            className="w-full glass hover:bg-white/15 text-white font-medium touch-feedback transition-all duration-200 border-white/20 hover:border-white/30"
            variant="outline"
          >
            <i className="fab fa-google mr-2 text-red-400"></i>
            Continue with Google
          </Button>

          {/* Test Login Button */}
          <Button
            onClick={handleTestLogin}
            disabled={loading}
            className="w-full bg-green-600 hover:bg-green-700 text-white font-medium touch-feedback transition-all duration-200"
          >
            <i className="fas fa-flask mr-2"></i>
            Quick Test Login
          </Button>

          {/* Debug Button */}
          <Button
            onClick={checkAuthState}
            className="w-full glass hover:bg-white/15 text-white font-medium touch-feedback transition-all duration-200 border-white/20 hover:border-white/30"
            variant="outline"
          >
            <i className="fas fa-bug mr-2 text-yellow-400"></i>
            Debug Auth State
          </Button>
        </div>

        <div className="text-center mt-6 slide-up">
          <p className="text-white/60 text-sm">
            {isSignUp ? "Already have an account?" : "Don't have an account?"}{" "}
            <button
              type="button"
              onClick={() => setIsSignUp(!isSignUp)}
              className="text-white hover:text-purple-300 smooth-transition underline font-medium touch-feedback"
            >
              {isSignUp ? "Sign in" : "Sign up"}
            </button>
          </p>

          {/* Features Preview */}
          <div className="mt-6 pt-4 border-t border-white/10">
            <div className="grid grid-cols-2 gap-3 text-xs text-white/50">
              <div className="flex items-center space-x-1">
                <i className="fas fa-shield-alt text-green-400"></i>
                <span>Secure</span>
              </div>
              <div className="flex items-center space-x-1">
                <i className="fas fa-sync text-blue-400"></i>
                <span>Real-time</span>
              </div>
              <div className="flex items-center space-x-1">
                <i className="fas fa-mobile-alt text-purple-400"></i>
                <span>Mobile Ready</span>
              </div>
              <div className="flex items-center space-x-1">
                <i className="fas fa-users text-pink-400"></i>
                <span>Team Collaboration</span>
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>
  );
}
