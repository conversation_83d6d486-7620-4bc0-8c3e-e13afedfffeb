import { useState, useEffect } from "react";
import { rtdb } from "@/lib/firebase";
import { ref, push, set, onValue, off, serverTimestamp } from "firebase/database";
import { useAuth } from "./useAuth";
import { handlePotentialOfflineError, getTasksOfflineFirst, getUsersOfflineFirst } from "@/lib/offlineMode";

export function useRealtimeTasks() {
  const { user } = useAuth();
  const [tasks, setTasks] = useState<any[]>([]);
  const [loading, setLoading] = useState(false); // Start with false to skip loading

  useEffect(() => {
    if (!user) {
      setTasks([]);
      return;
    }

    console.log('useRealtimeTasks: Starting to load tasks for user:', user.uid);

    // Load offline data immediately
    const offlineTasks = getTasksOfflineFirst().filter(task => task.userId === user.uid);
    setTasks(offlineTasks);

    try {
      const tasksRef = ref(rtdb, 'tasks');
      const unsubscribe = onValue(
        tasksRef,
        (snapshot) => {
          console.log('useRealtimeTasks: Received data from Firebase');
          const data = snapshot.val();
          if (data) {
            const userTasks = Object.entries(data)
              .filter(([_, task]: [string, any]) => task.userId === user.uid)
              .map(([id, task]) => ({ id, ...(task as any) }))
              .sort((a: any, b: any) => new Date(b.createdAt).getTime() - new Date(a.createdAt).getTime());
            setTasks(userTasks);
          } else {
            setTasks([]);
          }
        },
        (error) => {
          console.error('useRealtimeTasks: Firebase error:', error);

          // Handle offline error
          if (handlePotentialOfflineError(error)) {
            const offlineTasks = getTasksOfflineFirst().filter(task => task.userId === user.uid);
            setTasks(offlineTasks);
          } else {
            setTasks([]);
          }
        }
      );

      return () => {
        off(tasksRef, 'value', unsubscribe);
      };
    } catch (error) {
      console.error('useRealtimeTasks: Setup error:', error);

      // Fallback to offline data
      const offlineTasks = getTasksOfflineFirst().filter(task => task.userId === user.uid);
      setTasks(offlineTasks);
    }
  }, [user]);

  const addTask = async (taskData: any) => {
    if (!user) return;

    const tasksRef = ref(rtdb, 'tasks');
    const newTaskRef = push(tasksRef);

    await set(newTaskRef, {
      ...taskData,
      userId: user.uid,
      status: "active",
      createdAt: new Date().toISOString(),
      updatedAt: new Date().toISOString()
    });
  };

  const updateTask = async (id: string, updates: any) => {
    const taskRef = ref(rtdb, `tasks/${id}`);
    await set(taskRef, {
      ...updates,
      updatedAt: new Date().toISOString()
    });
  };

  const deleteTask = async (id: string) => {
    const taskRef = ref(rtdb, `tasks/${id}`);
    await set(taskRef, null);
  };

  return { tasks, loading, addTask, updateTask, deleteTask };
}

export function useRealtimeMessages() {
  const [messages, setMessages] = useState<any[]>([]);
  const [loading, setLoading] = useState(false); // Start with false to skip loading

  useEffect(() => {
    console.log('useRealtimeMessages: Starting to load messages');

    // Set a timeout to prevent infinite loading
    const loadingTimeout = setTimeout(() => {
      console.log('useRealtimeMessages: Loading timeout reached, using empty array');
      setMessages([]);
      setLoading(false);
    }, 5000);

    try {
      const messagesRef = ref(rtdb, 'messages');
      const unsubscribe = onValue(
        messagesRef,
        (snapshot) => {
          console.log('useRealtimeMessages: Received data from Firebase');
          clearTimeout(loadingTimeout);
          const data = snapshot.val();
          if (data) {
            const messagesList = Object.entries(data)
              .map(([id, message]) => ({ id, ...(message as any) }))
              .sort((a: any, b: any) => new Date(b.createdAt).getTime() - new Date(a.createdAt).getTime());
            setMessages(messagesList);
          } else {
            setMessages([]);
          }
          setLoading(false);
        },
        (error) => {
          console.error('useRealtimeMessages: Firebase error:', error);
          clearTimeout(loadingTimeout);
          handlePotentialOfflineError(error);
          setMessages([]);
          setLoading(false);
        }
      );

      return () => {
        clearTimeout(loadingTimeout);
        off(messagesRef, 'value', unsubscribe);
      };
    } catch (error) {
      console.error('useRealtimeMessages: Setup error:', error);
      clearTimeout(loadingTimeout);
      setMessages([]);
      setLoading(false);
    }
  }, []);

  const addMessage = async (messageData: any) => {
    const messagesRef = ref(rtdb, 'messages');
    const newMessageRef = push(messagesRef);

    await set(newMessageRef, {
      ...messageData,
      createdAt: new Date().toISOString()
    });
  };

  return { messages, loading, addMessage };
}

export function useRealtimeChat() {
  const { user } = useAuth();
  const [chatMessages, setChatMessages] = useState<any[]>([]);
  const [loading, setLoading] = useState(false); // Start with false to skip loading

  useEffect(() => {
    console.log('useRealtimeChat: Starting to load chat messages');

    // Set a timeout to prevent infinite loading
    const loadingTimeout = setTimeout(() => {
      console.log('useRealtimeChat: Loading timeout reached, using empty array');
      setChatMessages([]);
      setLoading(false);
    }, 5000);

    try {
      const chatRef = ref(rtdb, 'chat');
      const unsubscribe = onValue(
        chatRef,
        (snapshot) => {
          console.log('useRealtimeChat: Received data from Firebase');
          clearTimeout(loadingTimeout);
          const data = snapshot.val();
          if (data) {
            const messagesList = Object.entries(data)
              .map(([id, message]) => ({ id, ...(message as any) }))
              .sort((a: any, b: any) => new Date(a.timestamp).getTime() - new Date(b.timestamp).getTime());
            setChatMessages(messagesList);
          } else {
            setChatMessages([]);
          }
          setLoading(false);
        },
        (error) => {
          console.error('useRealtimeChat: Firebase error:', error);
          clearTimeout(loadingTimeout);
          handlePotentialOfflineError(error);
          setChatMessages([]);
          setLoading(false);
        }
      );

      return () => {
        clearTimeout(loadingTimeout);
        off(chatRef, 'value', unsubscribe);
      };
    } catch (error) {
      console.error('useRealtimeChat: Setup error:', error);
      clearTimeout(loadingTimeout);
      setChatMessages([]);
      setLoading(false);
    }
  }, []);

  const sendMessage = async (message: string) => {
    if (!user || !message.trim()) return;

    const chatRef = ref(rtdb, 'chat');
    const newMessageRef = push(chatRef);

    await set(newMessageRef, {
      message: message.trim(),
      userId: user.uid,
      displayName: user.displayName || user.email,
      timestamp: new Date().toISOString()
    });
  };

  return { chatMessages, loading, sendMessage };
}

// Alias for compatibility
export const useTasks = useRealtimeTasks;

export function useAdminRealtimeTasks() {
  const { user } = useAuth();
  const [allTasks, setAllTasks] = useState<any[]>([]);
  const [allUsers, setAllUsers] = useState<any[]>([]);
  const [loading, setLoading] = useState(false); // Start with false to skip loading

  useEffect(() => {
    if (!user) {
      setAllTasks([]);
      setAllUsers([]);
      setLoading(false);
      return;
    }

    console.log('useAdminRealtimeTasks: Starting to load admin data');

    // Set a timeout to prevent infinite loading
    const loadingTimeout = setTimeout(() => {
      console.log('useAdminRealtimeTasks: Loading timeout reached, using offline data');
      const offlineTasks = getTasksOfflineFirst();
      const offlineUsers = getUsersOfflineFirst();
      setAllTasks(offlineTasks);
      setAllUsers(offlineUsers);
      setLoading(false);
    }, 5000);

    try {
      // Listen to all tasks for admin
      const tasksRef = ref(rtdb, 'tasks');
      const unsubscribeTasks = onValue(
        tasksRef,
        (snapshot) => {
          console.log('useAdminRealtimeTasks: Received tasks data from Firebase');
          const data = snapshot.val();
          if (data) {
            const tasksList = Object.entries(data)
              .map(([id, task]) => ({ id, ...(task as any) }))
              .sort((a: any, b: any) => new Date(b.createdAt).getTime() - new Date(a.createdAt).getTime());
            setAllTasks(tasksList);
          } else {
            setAllTasks([]);
          }
        },
        (error) => {
          console.error('useAdminRealtimeTasks: Tasks Firebase error:', error);
          if (handlePotentialOfflineError(error)) {
            const offlineTasks = getTasksOfflineFirst();
            setAllTasks(offlineTasks);
          }
        }
      );

      // Listen to all users for admin
      const usersRef = ref(rtdb, 'users');
      const unsubscribeUsers = onValue(
        usersRef,
        (snapshot) => {
          console.log('useAdminRealtimeTasks: Received users data from Firebase');
          clearTimeout(loadingTimeout);
          const data = snapshot.val();
          if (data) {
            const usersList = Object.entries(data).map(([id, user]) => ({ id, ...(user as any) }));
            setAllUsers(usersList);
          } else {
            setAllUsers([]);
          }
          setLoading(false);
        },
        (error) => {
          console.error('useAdminRealtimeTasks: Users Firebase error:', error);
          clearTimeout(loadingTimeout);
          if (handlePotentialOfflineError(error)) {
            const offlineUsers = getUsersOfflineFirst();
            setAllUsers(offlineUsers);
          }
          setLoading(false);
        }
      );

      return () => {
        clearTimeout(loadingTimeout);
        off(tasksRef, 'value', unsubscribeTasks);
        off(usersRef, 'value', unsubscribeUsers);
      };
    } catch (error) {
      console.error('useAdminRealtimeTasks: Setup error:', error);
      clearTimeout(loadingTimeout);
      const offlineTasks = getTasksOfflineFirst();
      const offlineUsers = getUsersOfflineFirst();
      setAllTasks(offlineTasks);
      setAllUsers(offlineUsers);
      setLoading(false);
    }
  }, [user]);

  const adminDeleteTask = async (taskId: string, taskDetails: any) => {
    if (!user) return;

    // Log admin action
    const adminActionsRef = ref(rtdb, 'adminActions');
    const newActionRef = push(adminActionsRef);
    await set(newActionRef, {
      adminId: user.uid,
      action: "delete_task",
      targetId: taskId,
      details: JSON.stringify(taskDetails),
      timestamp: new Date().toISOString()
    });

    // Delete the task
    const taskRef = ref(rtdb, `tasks/${taskId}`);
    await set(taskRef, null);
  };

  const adminUpdateTask = async (taskId: string, updates: any, originalTask: any) => {
    if (!user) return;

    // Log admin action
    const adminActionsRef = ref(rtdb, 'adminActions');
    const newActionRef = push(adminActionsRef);
    await set(newActionRef, {
      adminId: user.uid,
      action: "modify_task",
      targetId: taskId,
      details: JSON.stringify({ original: originalTask, updates }),
      timestamp: new Date().toISOString()
    });

    // Update the task
    const taskRef = ref(rtdb, `tasks/${taskId}`);
    await set(taskRef, {
      ...updates,
      updatedAt: new Date().toISOString(),
      lastModifiedBy: user.uid
    });
  };

  const getUserById = (userId: string) => {
    return allUsers.find(u => u.uid === userId || u.id === userId);
  };

  return {
    allTasks,
    allUsers,
    loading,
    adminDeleteTask,
    adminUpdateTask,
    getUserById
  };
}