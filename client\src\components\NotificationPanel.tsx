import { useEffect, useRef } from "react";

interface NotificationPanelProps {
  onClose: () => void;
}

export default function NotificationPanel({ onClose }: NotificationPanelProps) {
  const panelRef = useRef<HTMLDivElement>(null);

  useEffect(() => {
    const handleClickOutside = (event: MouseEvent) => {
      if (panelRef.current && !panelRef.current.contains(event.target as Node)) {
        onClose();
      }
    };

    document.addEventListener("mousedown", handleClickOutside);
    return () => document.removeEventListener("mousedown", handleClickOutside);
  }, [onClose]);

  // Mock notifications
  const notifications = [
    {
      id: "1",
      type: "info",
      message: "New team message from <PERSON><PERSON>",
      time: "2 minutes ago",
      icon: "fas fa-info",
      color: "bg-blue-500",
    },
    {
      id: "2",
      type: "success",
      message: "Task completed: Review code submissions",
      time: "1 hour ago",
      icon: "fas fa-check",
      color: "bg-green-500",
    },
    {
      id: "3",
      type: "warning",
      message: "Reminder: Project deadline approaching",
      time: "3 hours ago",
      icon: "fas fa-exclamation",
      color: "bg-yellow-500",
    },
  ];

  return (
    <div ref={panelRef} className="fixed top-20 right-4 w-80 glass rounded-2xl p-6 z-40">
      <h3 className="text-lg font-semibold text-white mb-4">Notifications</h3>
      <div className="space-y-3">
        {notifications.map((notification) => (
          <div key={notification.id} className="glass-dark rounded-xl p-3">
            <div className="flex items-start space-x-3">
              <div
                className={`w-8 h-8 ${notification.color} rounded-full flex items-center justify-center flex-shrink-0`}
              >
                <i className={`${notification.icon} text-white text-xs`}></i>
              </div>
              <div className="flex-1">
                <p className="text-white/90 text-sm">{notification.message}</p>
                <p className="text-white/50 text-xs mt-1">{notification.time}</p>
              </div>
            </div>
          </div>
        ))}
      </div>
    </div>
  );
}
